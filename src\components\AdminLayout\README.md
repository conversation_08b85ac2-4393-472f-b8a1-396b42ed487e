# AdminLayout 通用管理界面布局组件

## 概述

AdminLayout 是一个通用的管理界面布局组件，提供了标准的左侧区域树、右上类型切换、右下列表内容的布局结构。该组件已被应用到历史要素管理、山塬管理和水系管理等多个页面。

## 功能特性

- 🌳 **左侧区域树**: 自动加载区域字典数据，支持树形选择
- 🔄 **类型切换**: 右上角提供灵活的类型切换按钮（如数据列表、统计分析、时间轴等）
- 📋 **内容区域**: 右下角显示对应类型的内容
- 🎨 **响应式设计**: 支持不同屏幕尺寸的自适应布局
- 🌙 **深色主题**: 支持深色主题模式

## 基本用法

```tsx
import AdminLayout, { TypeOption } from '@/components/AdminLayout';
import { RegionTreeProvider } from '@/components/AdminLayout/hooks/useRegionTree';

const YourManagementPage: React.FC = () => {
  const [selectedRegion, setSelectedRegion] = useState<React.Key | undefined>();
  const [activeTab, setActiveTab] = useState('list');

  // 定义类型切换选项
  const typeOptions: TypeOption[] = [
    {
      label: '数据列表',
      value: 'list',
      icon: <TableOutlined />,
    },
    {
      label: '统计分析',
      value: 'statistics',
      icon: <BarChartOutlined />,
    },
  ];

  // 处理区域选择
  const handleRegionSelect = (selectedKeys: React.Key[]) => {
    setSelectedRegion(selectedKeys[0]);
    // 处理区域筛选逻辑
  };

  // 渲染内容
  const renderContent = () => {
    switch (activeTab) {
      case 'list':
        return <YourListComponent />;
      case 'statistics':
        return <YourStatisticsComponent />;
      default:
        return null;
    }
  };

  return (
    <RegionTreeProvider>
      {({ treeData }) => (
        <AdminLayout
          title="您的管理页面"
          regionTreeData={treeData}
          selectedRegion={selectedRegion}
          onRegionSelect={handleRegionSelect}
          typeOptions={typeOptions}
          selectedType={activeTab}
          onTypeChange={setActiveTab}
          extra={
            <Button type="primary" icon={<PlusOutlined />}>
              添加数据
            </Button>
          }
        >
          {renderContent()}
        </AdminLayout>
      )}
    </RegionTreeProvider>
  );
};
```

## API 参考

### AdminLayoutProps

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | string | - | 页面标题 |
| regionTreeData | DataNode[] | [] | 区域树数据 |
| selectedRegion | React.Key | - | 选中的区域 |
| onRegionSelect | (selectedKeys: React.Key[], info: any) => void | - | 区域选择回调 |
| typeOptions | TypeOption[] | - | 类型切换选项 |
| selectedType | string | - | 当前选中的类型 |
| onTypeChange | (value: string) => void | - | 类型切换回调 |
| children | React.ReactNode | - | 右下角内容区域 |
| extra | React.ReactNode | - | 额外的操作按钮 |
| leftSpan | number | 6 | 左侧区域占用的栅格数 |
| rightSpan | number | 18 | 右侧区域占用的栅格数 |
| className | string | - | 自定义样式类名 |
| style | React.CSSProperties | - | 自定义样式 |

### TypeOption

| 属性 | 类型 | 说明 |
|------|------|------|
| label | string | 显示文本 |
| value | string | 选项值 |
| icon | React.ReactNode | 图标（可选） |

## 使用示例

### 1. 历史要素管理页面

```tsx
// src/pages/Admin/HistoricalElement/index.tsx
const typeOptions: TypeOption[] = [
  { label: '数据列表', value: 'list', icon: <TableOutlined /> },
  { label: '统计分析', value: 'statistics', icon: <BarChartOutlined /> },
  { label: '时间轴', value: 'timeline', icon: <HistoryOutlined /> },
];
```

### 2. 水系管理页面

```tsx
// src/pages/Admin/WaterSystem/index.tsx
const typeOptions: TypeOption[] = [
  { label: '数据列表', value: 'list', icon: <TableOutlined /> },
  { label: '统计分析', value: 'statistics', icon: <BarChartOutlined /> },
  { label: '时间轴', value: 'timeline', icon: <HistoryOutlined /> },
];
```

## 样式定制

组件使用 CSS Modules，可以通过以下方式自定义样式：

```less
// 自定义样式文件
.customLayout {
  :global(.adminLayout) {
    background-color: #f0f2f5;
    
    .regionCard {
      border-radius: 8px;
    }
    
    .typeSegmented {
      :global(.ant-segmented-item) {
        font-weight: 600;
      }
    }
  }
}
```

## 注意事项

1. **RegionTreeProvider**: 必须使用 `RegionTreeProvider` 包装组件以获取区域数据
2. **响应式布局**: 在小屏幕设备上，左侧区域树会自动调整布局
3. **数据加载**: 区域数据会自动从 dva store 中加载，确保 dictionary model 已正确配置
4. **类型切换**: `typeOptions` 和 `selectedType` 必须保持同步

## 相关文件

- `src/components/AdminLayout/index.tsx` - 主组件
- `src/components/AdminLayout/index.module.less` - 样式文件
- `src/components/AdminLayout/hooks/useRegionTree.ts` - 区域数据Hook
- `src/models/dictionary.ts` - 字典数据模型
