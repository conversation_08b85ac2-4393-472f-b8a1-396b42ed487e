/**
 * @file 区域树数据Hook
 * @description 处理区域字典数据转换为Tree组件所需的数据格式
 * <AUTHOR> Assistant
 * @date 2025-08-30
 */

import { DictionaryState } from '@/models/dictionary';
import { connect, useDispatch } from '@umijs/max';
import type { DataNode } from 'antd/es/tree';
import { useCallback, useEffect, useMemo } from 'react';

interface RegionTreeHookProps {
  dictionary: DictionaryState;
}

interface UseRegionTreeReturn {
  treeData: DataNode[];
  loading: boolean;
  loadRegionData: () => void;
}

const useRegionTreeInternal = ({ dictionary }: RegionTreeHookProps): UseRegionTreeReturn => {
  const dispatch = useDispatch();

  // 加载区域数据
  const loadRegionData = useCallback(() => {
    if (dictionary.regionList.length === 0) {
      dispatch({ type: 'dictionary/fetchRegionList' });
    }
  }, [dictionary.regionList.length, dispatch]);

  // 初始化时加载数据
  useEffect(() => {
    loadRegionData();
  }, [loadRegionData]);

  // 转换区域数据为Tree组件格式
  const treeData = useMemo(() => {
    const buildTreeData = (items: any[], parentId?: number | null): DataNode[] => {
      return items
        .filter((item) => {
          // 处理根节点
          const isRoot = parentId === undefined || parentId === null;
          const itemIsRoot = item.parentId === null || item.parentId === undefined;
          
          if (isRoot) {
            return itemIsRoot;
          } else {
            return item.parentId === parentId;
          }
        })
        .map((item) => ({
          title: item.regionName,
          key: item.id,
          value: item.id,
          children: buildTreeData(items, item.id),
          // 添加原始数据，方便后续使用
          data: item,
        }));
    };

    return buildTreeData(dictionary.regionList);
  }, [dictionary.regionList]);

  return {
    treeData,
    loading: dictionary.loading,
    loadRegionData,
  };
};

// 导出连接dva的组件版本
export const RegionTreeProvider = connect(
  ({ dictionary }: { dictionary: DictionaryState }) => ({
    dictionary,
  }),
)(({ dictionary, children }: { dictionary: DictionaryState; children: (hookReturn: UseRegionTreeReturn) => React.ReactNode }) => {
  const hookReturn = useRegionTreeInternal({ dictionary });
  return children(hookReturn) as React.ReactElement;
});

// 简单的Hook版本（不连接dva，需要手动传入dictionary）
export const useRegionTree = (dictionary: DictionaryState): UseRegionTreeReturn => {
  return useRegionTreeInternal({ dictionary });
};
