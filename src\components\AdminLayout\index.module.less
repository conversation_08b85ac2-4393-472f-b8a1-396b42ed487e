/* 通用管理界面布局样式 */

@primary-color: #1890ff;
@border-color-base: #d9d9d9;
@border-color-light: #f0f0f0;
@text-color: #262626;
@text-color-secondary: #595959;
@box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
@border-radius-base: 6px;

.adminLayout {
  padding: 24px;
  min-height: 100vh;
  background-color: #f5f5f5;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 0 4px;

    .title {
      margin: 0;
      color: @text-color;
      font-weight: 600;
    }

    .extra {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .content {
    min-height: calc(100vh - 120px);

    .regionCard {
      height: 100%;
      border-radius: @border-radius-base;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
      
      :global(.ant-card-head) {
        border-bottom: 1px solid @border-color-light;
        
        .ant-card-head-title {
          font-weight: 600;
          color: @text-color;
          font-size: 14px;
        }
      }

      .regionTree {
        max-height: calc(100vh - 200px);
        overflow-y: auto;
        
        // 自定义滚动条
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }

        // 树节点样式
        :global(.ant-tree-node-content-wrapper) {
          padding: 4px 8px;
          border-radius: 4px;
          transition: all 0.2s ease;

          &:hover {
            background-color: rgba(24, 144, 255, 0.1);
          }

          &.ant-tree-node-selected {
            background-color: rgba(24, 144, 255, 0.15);
            color: @primary-color;
            font-weight: 500;
          }
        }

        :global(.ant-tree-title) {
          font-size: 13px;
        }
      }
    }

    .rightContent {
      display: flex;
      flex-direction: column;
      height: 100%;
      gap: 16px;

      .typeCard {
        border-radius: @border-radius-base;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

        .typeSegmented {
          width: 100%;
          
          :global(.ant-segmented-item) {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-weight: 500;
            transition: all 0.2s ease;

            &:hover {
              color: @primary-color;
            }
          }

          :global(.ant-segmented-item-selected) {
            background-color: @primary-color;
            color: white;
            font-weight: 600;
          }
        }
      }

      .listContent {
        flex: 1;
        background: white;
        border-radius: @border-radius-base;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
        overflow: hidden;
        
        // 确保内容区域能够滚动
        > * {
          height: 100%;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .adminLayout {
    .content {
      .regionCard {
        margin-bottom: 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .adminLayout {
    padding: 16px;

    .header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .title {
        font-size: 20px;
      }

      .extra {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .content {
      .regionCard {
        .regionTree {
          max-height: 300px;
        }
      }

      .rightContent {
        .typeCard {
          .typeSegmented {
            :global(.ant-segmented-item) {
              font-size: 12px;
              padding: 8px 12px;
            }
          }
        }
      }
    }
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .adminLayout {
    background-color: #141414;

    .regionCard,
    .typeCard,
    .listContent {
      background-color: #1f1f1f;
      border-color: #434343;
    }

    .header .title {
      color: #ffffff;
    }

    .regionTree {
      :global(.ant-tree-node-content-wrapper) {
        color: #ffffff;

        &:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        &.ant-tree-node-selected {
          background-color: rgba(24, 144, 255, 0.2);
          color: #40a9ff;
        }
      }
    }
  }
}
