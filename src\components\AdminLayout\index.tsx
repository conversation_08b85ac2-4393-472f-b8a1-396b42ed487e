/**
 * @file 通用管理界面布局组件
 * @description 提供左侧区域树、右上类型切换、右下列表的标准布局
 * <AUTHOR> Assistant
 * @date 2025-08-30
 */

import DictSelect from '@/components/DictSelect';
import { Card, Col, Row, Segmented, Tree, Typography } from 'antd';
import type { DataNode } from 'antd/es/tree';
import React from 'react';
import styles from './index.module.less';

const { Title } = Typography;

// 类型切换选项接口
export interface TypeOption {
  label: string;
  value: string;
  icon?: React.ReactNode;
}

// 布局组件属性接口
export interface AdminLayoutProps {
  // 页面标题
  title: string;
  
  // 左侧区域树
  regionTreeData?: DataNode[];
  selectedRegion?: React.Key;
  onRegionSelect?: (selectedKeys: React.Key[], info: any) => void;
  
  // 右上角类型切换
  typeOptions: TypeOption[];
  selectedType: string;
  onTypeChange: (value: string) => void;
  
  // 右下角内容区域
  children: React.ReactNode;
  
  // 额外的操作按钮
  extra?: React.ReactNode;
  
  // 布局配置
  leftSpan?: number;
  rightSpan?: number;
  
  // 样式配置
  className?: string;
  style?: React.CSSProperties;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({
  title,
  regionTreeData = [],
  selectedRegion,
  onRegionSelect,
  typeOptions,
  selectedType,
  onTypeChange,
  children,
  extra,
  leftSpan = 6,
  rightSpan = 18,
  className,
  style,
}) => {
  return (
    <div className={`${styles.adminLayout} ${className || ''}`} style={style}>
      {/* 页面标题和操作按钮 */}
      <div className={styles.header}>
        <Title level={2} className={styles.title}>
          {title}
        </Title>
        {extra && <div className={styles.extra}>{extra}</div>}
      </div>

      {/* 主要内容区域 */}
      <Row gutter={16} className={styles.content}>
        {/* 左侧区域树 */}
        <Col span={leftSpan}>
          <Card 
            title="区域选择" 
            className={styles.regionCard}
            bodyStyle={{ padding: '12px' }}
          >
            <Tree
              treeData={regionTreeData}
              selectedKeys={selectedRegion ? [selectedRegion] : []}
              onSelect={onRegionSelect}
              showLine
              defaultExpandAll
              className={styles.regionTree}
            />
          </Card>
        </Col>

        {/* 右侧内容区域 */}
        <Col span={rightSpan}>
          <div className={styles.rightContent}>
            {/* 类型切换区域 */}
            <Card className={styles.typeCard} bodyStyle={{ padding: '16px' }}>
              <Segmented
                options={typeOptions}
                value={selectedType}
                onChange={onTypeChange}
                className={styles.typeSegmented}
              />
            </Card>

            {/* 列表内容区域 */}
            <div className={styles.listContent}>
              {children}
            </div>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default AdminLayout;
export type { TypeOption, AdminLayoutProps };
