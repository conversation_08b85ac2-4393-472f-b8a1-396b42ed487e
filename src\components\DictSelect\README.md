# DictSelect 字典选择组件

## 概述

DictSelect 是一个基于 dva 缓存的字典选择组件，可以在任何地方使用。它提供了树形选择和显示功能，支持区域、类型、关系三种字典类型。

## 功能特性

### 🌳 DictTreeSelect - 字典树形选择组件

- 支持三种字典类型：region（区域）、type（类型）、relation（关系）
- 基于 dva 全局状态管理，自动缓存字典数据
- 支持树形结构显示和选择
- 支持搜索过滤
- 支持禁用状态显示
- 支持排除特定节点

### 📄 DictDisplay - 字典显示组件

- 根据字典类型和 ID 显示字典名称
- 支持显示编码
- 支持显示完整路径
- 自动处理数据不存在的情况

## 使用方法

### 基本用法

```tsx
import DictSelect from '@/components/DictSelect';

// 树形选择组件
<DictSelect.DictTreeSelect
  type="region"
  value={value}
  onChange={setValue}
  placeholder="请选择区域"
  style={{ width: 300 }}
/>

// 显示组件
<DictSelect.DictDisplay
  type="region"
  id={regionId}
  showCode
  showPath
/>
```

### 在表单中使用

```tsx
import { Form } from 'antd';
import DictSelect from '@/components/DictSelect';

<Form.Item name="regionId" label="区域">
  <DictSelect.DictTreeSelect
    type="region"
    placeholder="请选择区域"
    allowClear
    style={{ width: '100%' }}
  />
</Form.Item>;
```

## API 文档

### DictTreeSelect Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| type | 字典类型 | `'region' \| 'type' \| 'relation'` | - |
| value | 选中的值 | `number` | - |
| onChange | 值变化回调 | `(value: number) => void` | - |
| placeholder | 占位符 | `string` | 自动生成 |
| allowClear | 是否允许清除 | `boolean` | `true` |
| showSearch | 是否显示搜索 | `boolean` | `true` |
| disabled | 是否禁用 | `boolean` | `false` |
| style | 样式 | `React.CSSProperties` | - |
| excludeId | 排除的节点 ID | `number` | - |
| treeDefaultExpandAll | 是否默认展开所有节点 | `boolean` | `false` |

### DictDisplay Props

| 参数     | 说明             | 类型                               | 默认值  |
| -------- | ---------------- | ---------------------------------- | ------- |
| type     | 字典类型         | `'region' \| 'type' \| 'relation'` | -       |
| id       | 字典项 ID        | `number`                           | -       |
| showCode | 是否显示编码     | `boolean`                          | `false` |
| showPath | 是否显示完整路径 | `boolean`                          | `false` |

## 数据流

1. 组件首次使用时，会自动调用 dva action 加载对应类型的字典数据
2. 数据加载完成后缓存在全局 store 中
3. 后续使用直接从缓存读取，无需重复请求
4. 组件会自动转换数据格式为 TreeSelect 所需的格式

## 已应用的页面

### 字典管理页面

- 路径：`/admin/dictionary`
- 应用场景：在添加/编辑字典项时选择父级字典
- 替换了原有的 TreeSelect 组件

### 资源管理页面

- 路径：`/admin/upload`
- 应用场景：在编辑照片信息时关联字典项
- 替换了原有的 Select 组件

## 技术实现

### 核心 Hook: useDictUtils

提供以下功能：

- `loadDictByType`: 加载指定类型的字典数据
- `convertToTreeSelectOptions`: 转换数据为树形选择格式
- `findDictById`: 根据 ID 查找字典项
- `getDictName`: 获取字典名称
- `getDictPath`: 获取字典完整路径

### dva Model 集成

- 使用 `connect` 高阶组件连接 dva store
- 自动注入 `dictionary` 状态
- 支持 loading 状态显示

## 测试

### 演示页面

- 路径：`/admin/dict-demo`
- 提供完整的组件功能演示
- 可以测试三种字典类型的选择和显示

### 开发调试

```bash
npm run dev
# 访问 http://localhost:8000/admin/dict-demo
```

## 注意事项

1. 确保 dva model `dictionary` 已正确配置
2. 确保字典相关的 API 服务正常
3. 组件依赖 Antd TreeSelect，确保版本兼容
4. 使用前需要先登录并有相应权限

## 更新日志

### v1.0.0 (2025-08-30)

- ✅ 完成基础组件开发
- ✅ 集成 dva 状态管理
- ✅ 应用到字典管理页面
- ✅ 应用到资源管理页面
- ✅ 创建演示页面
- ✅ 完善文档说明
