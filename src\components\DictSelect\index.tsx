/**
 * @file 字典选择组件
 * @description 基于dva缓存的字典选择组件，可以在任何地方使用
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */
import { DictionaryState } from '@/models/dictionary';
import { DictType } from '@/pages/Admin/Dictionary/dict-types';
import { connect, useDispatch } from '@umijs/max';
import { TreeSelect } from 'antd';
import React, { useCallback, useEffect, useMemo } from 'react';

// 树形选择数据接口
interface TreeSelectOption {
  title: string;
  value: number;
  key: number;
  children?: TreeSelectOption[];
  disabled?: boolean;
}

interface DictTreeSelectProps {
  type: DictType;
  value?: number;
  onChange?: (value: number) => void;
  placeholder?: string;
  allowClear?: boolean;
  showSearch?: boolean;
  disabled?: boolean;
  style?: React.CSSProperties;
  onlyEnabled?: boolean;
  excludeId?: number;
  dictionary: DictionaryState;
  treeDefaultExpandAll?: boolean;
}

// 字典工具函数Hook
const useDictUtils = (dictionary: DictionaryState, dispatch: any) => {
  // 加载字典数据
  const loadDictByType = useCallback(
    (type: DictType) => {
      switch (type) {
        case 'region':
          if (dictionary.regionList.length === 0) {
            dispatch({ type: 'dictionary/fetchRegionList' });
          }
          break;
        case 'type':
          if (dictionary.typeList.length === 0) {
            dispatch({ type: 'dictionary/fetchTypeList' });
          }
          break;
        case 'relation':
          if (dictionary.relationList.length === 0) {
            dispatch({ type: 'dictionary/fetchRelationList' });
          }
          break;
      }
    },
    [dictionary, dispatch],
  );

  // 转换为TreeSelect选项
  const convertToTreeSelectOptions = useCallback(
    (
      type: DictType,
      parentId?: number,
      excludeId?: number,
    ): TreeSelectOption[] => {
      let dataList: any[] = [];
      let nameField = '';

      switch (type) {
        case 'region':
          dataList = dictionary.regionList;
          nameField = 'regionName';
          break;
        case 'type':
          dataList = dictionary.typeList;
          nameField = 'typeName';
          break;
        case 'relation':
          dataList = dictionary.relationList;
          nameField = 'relationName';
          break;
      }

      const buildTree = (items: any[], pid?: number | null): TreeSelectOption[] => {
        console.log('buildTree called with:', { itemsLength: items.length, pid, type, nameField });
        console.log('first few items:', items.slice(0, 3));

        const filtered = items.filter((item) => {
          // 处理根节点：parentId为null或undefined都视为根节点
          const isRoot = pid === undefined || pid === null;
          const itemIsRoot = item.parentId === null || item.parentId === undefined;

          if (isRoot) {
            return itemIsRoot && item.id !== excludeId;
          } else {
            return item.parentId === pid && item.id !== excludeId;
          }
        });

        console.log('filtered items:', filtered.length);

        return filtered.map((item) => ({
          title: item[nameField],
          value: item.id,
          key: item.id,
          disabled: item.status === 0,
          children: buildTree(items, item.id),
        }));
      };

      return buildTree(dataList, parentId);
    },
    [dictionary],
  );

  // 根据ID查找字典项
  const findDictById = useCallback(
    (type: DictType, id: number) => {
      let dataList: any[] = [];

      switch (type) {
        case 'region':
          dataList = dictionary.regionList;
          break;
        case 'type':
          dataList = dictionary.typeList;
          break;
        case 'relation':
          dataList = dictionary.relationList;
          break;
      }

      return dataList.find((item) => item.id === id);
    },
    [dictionary],
  );

  // 获取字典名称
  const getDictName = useCallback(
    (type: DictType, id: number) => {
      const item = findDictById(type, id);
      if (!item) return '';

      switch (type) {
        case 'region':
          return item.regionName;
        case 'type':
          return item.typeName;
        case 'relation':
          return item.relationName;
        default:
          return '';
      }
    },
    [findDictById],
  );

  // 获取字典路径
  const getDictPath = useCallback(
    (type: DictType, id: number): string[] => {
      const path: string[] = [];
      let currentId = id;

      while (currentId) {
        const item = findDictById(type, currentId);
        if (!item) break;

        const name = getDictName(type, currentId);
        if (name) {
          path.unshift(name);
        }

        currentId = item.parentId;
      }

      return path;
    },
    [findDictById, getDictName],
  );

  return {
    loadDictByType,
    convertToTreeSelectOptions,
    findDictById,
    getDictName,
    getDictPath,
    loading: dictionary.loading,
  };
};

/**
 * 字典树形选择组件
 */
const DictTreeSelect: React.FC<DictTreeSelectProps> = ({
  type,
  value,
  onChange,
  placeholder,
  allowClear = true,
  showSearch = true,
  disabled = false,
  style,
  excludeId,
  treeDefaultExpandAll = false,
  dictionary,
}) => {
  const dispatch = useDispatch();
  const { convertToTreeSelectOptions, loadDictByType, loading } = useDictUtils(
    dictionary,
    dispatch,
  );

  useEffect(() => {
    loadDictByType(type);
  }, [type]);

  const treeData = useMemo(
    () => convertToTreeSelectOptions(type, undefined, excludeId),
    [convertToTreeSelectOptions, type, excludeId],
  );
  console.log('type', type);
  console.log('dictionary', dictionary);
  console.log('treeData', treeData);

  return (
    <TreeSelect
      value={value}
      onChange={onChange}
      placeholder={
        placeholder ||
        `请选择${
          type === 'region' ? '区域' : type === 'type' ? '类型' : '关系'
        }`
      }
      allowClear={allowClear}
      showSearch={showSearch}
      disabled={disabled}
      loading={loading}
      style={style}
      treeData={treeData}
      treeDefaultExpandAll={treeDefaultExpandAll}
      filterTreeNode={(input, node) =>
        (node.title as string).toLowerCase().includes(input.toLowerCase())
      }
    />
  );
};

/**
 * 字典显示组件
 */
interface DictDisplayProps {
  type: DictType;
  id: number;
  showCode?: boolean;
  showPath?: boolean;
  dictionary: DictionaryState;
}

const DictDisplay: React.FC<DictDisplayProps> = ({
  type,
  id,
  showCode = false,
  showPath = false,
  dictionary,
}) => {
  const dispatch = useDispatch();
  const { findDictById, getDictName, getDictPath, loadDictByType } =
    useDictUtils(dictionary, dispatch);

  useEffect(() => {
    loadDictByType(type);
  }, [type, loadDictByType]);

  const item = findDictById(type, id);
  const name = getDictName(type, id);
  const path = showPath ? getDictPath(type, id) : [];

  if (!item) {
    return <span>-</span>;
  }

  const getCode = () => {
    return type === 'region'
      ? (item as any).regionCode
      : type === 'type'
      ? (item as any).typeCode
      : (item as any).relationCode;
  };

  const displayText = showCode ? `${name} (${getCode()})` : name;
  const fullText = showPath && path.length > 0 ? path.join(' > ') : displayText;

  return <span title={fullText}>{displayText}</span>;
};

// 连接dva的组件
const ConnectedDictTreeSelect = connect(
  ({ dictionary }: { dictionary: DictionaryState }) => ({
    dictionary,
  }),
)(DictTreeSelect);

const ConnectedDictDisplay = connect(
  ({ dictionary }: { dictionary: DictionaryState }) => ({
    dictionary,
  }),
)(DictDisplay);

export default {
  DictTreeSelect: ConnectedDictTreeSelect,
  DictDisplay: ConnectedDictDisplay,
};

// 也导出原始组件，方便直接使用
export { DictDisplay, DictTreeSelect };
