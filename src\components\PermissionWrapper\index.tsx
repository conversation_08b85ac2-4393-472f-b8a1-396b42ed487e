import { history, useAccess, useModel } from '@umijs/max';
import { Spin } from 'antd';
import React, { useEffect } from 'react';

interface PermissionWrapperProps {
  children: React.ReactNode;
  permission?: string;
  fallback?: React.ReactNode;
  redirect?: string;
}

const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
  children,
  permission,
  fallback,
  redirect = '/403',
}) => {
  const access = useAccess();
  const { initialState, loading } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  useEffect(() => {
    // 如果正在加载，等待加载完成
    if (loading) {
      return;
    }

    // 如果需要权限检查
    if (permission) {
      const hasPermission = access[permission];

      if (!hasPermission) {
        // 如果未登录，跳转到登录页
        if (!currentUser) {
          history.push('/admin/login');
          return;
        }

        // 如果已登录但无权限，跳转到403页面
        history.push(redirect);
        return;
      }
    }
  }, [loading, permission, access, currentUser, redirect]);

  // 正在加载时显示加载状态
  if (loading) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  // 如果需要权限检查
  if (permission) {
    const hasPermission = access[permission];

    if (!hasPermission) {
      // 返回fallback组件或null
      return fallback ? <>{fallback}</> : null;
    }
  }

  return <>{children}</>;
};

export default PermissionWrapper;
