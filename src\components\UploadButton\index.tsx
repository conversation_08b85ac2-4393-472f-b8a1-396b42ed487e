import { uploadFile, uploadUtils } from '@/services/upload';
import { LoadingOutlined, UploadOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import { Button, message, Upload } from 'antd';
import React, { useState } from 'react';

interface UploadButtonProps {
  onSuccess?: (fileInfo: {
    url: string;
    filename: string;
    size: number;
    photoId?: number;
  }) => void;
  onError?: (error: any) => void;
  accept?: string;
  maxSize?: number; // 字节
  buttonText?: string;
  buttonType?: 'default' | 'primary' | 'dashed' | 'link' | 'text';
  disabled?: boolean;
  multiple?: boolean;
  showUploadList?: boolean;
  // 照片记录创建选项
  createPhoto?: boolean;
  photoName?: string;
  entityType?: 'mountain' | 'waterSystem' | 'historicalElement';
  entityId?: number;
}

/**
 * 可复用的上传按钮组件
 */
const UploadButton: React.FC<UploadButtonProps> = ({
  onSuccess,
  onError,
  accept = 'image/*',
  maxSize = 50 * 1024 * 1024, // 50MB
  buttonText = '上传文件',
  buttonType = 'default',
  disabled = false,
  multiple = false,
  showUploadList = true,
  createPhoto = false,
  photoName,
  entityType,
  entityId,
}) => {
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const customUpload = async (options: any) => {
    const {
      file,
      onSuccess: uploadOnSuccess,
      onError: uploadOnError,
    } = options;

    // 验证文件
    const validation = uploadUtils.validateFile(file);
    if (!validation.valid) {
      message.error(validation.message);
      uploadOnError(new Error(validation.message));
      onError?.(new Error(validation.message));
      return;
    }

    // 检查文件大小
    if (file.size > maxSize) {
      const errorMsg = `文件大小超过限制（${uploadUtils.formatFileSize(
        maxSize,
      )}）`;
      message.error(errorMsg);
      uploadOnError(new Error(errorMsg));
      onError?.(new Error(errorMsg));
      return;
    }

    try {
      setLoading(true);

      // 构建上传选项
      const uploadOptions = createPhoto
        ? {
            createPhoto,
            photoName: photoName || file.name,
            entityType,
            entityId,
          }
        : undefined;

      const response = await uploadFile(file, uploadOptions);

      if (response.errCode === 0 && response.data) {
        const successMsg =
          createPhoto && response.data.photoId
            ? `${file.name} 上传成功，已创建照片记录`
            : `${file.name} 上传成功`;
        message.success(successMsg);
        uploadOnSuccess(response.data);
        onSuccess?.(response.data);
      } else {
        throw new Error(response.msg || '上传失败');
      }
    } catch (error: any) {
      console.error('上传失败:', error);
      message.error(error.message || `${file.name} 上传失败`);
      uploadOnError(error);
      onError?.(error);
    } finally {
      setLoading(false);
    }
  };

  const uploadProps: UploadProps = {
    customRequest: customUpload,
    fileList,
    multiple,
    accept,
    showUploadList,
    onChange: ({ fileList: newFileList }) => {
      setFileList(newFileList);
    },
    onRemove: (file) => {
      setFileList((prev) => prev.filter((item) => item.uid !== file.uid));
    },
    beforeUpload: (file) => {
      const validation = uploadUtils.validateFile(file);
      if (!validation.valid) {
        message.error(validation.message);
        return false;
      }

      if (file.size > maxSize) {
        message.error(
          `文件大小超过限制（${uploadUtils.formatFileSize(maxSize)}）`,
        );
        return false;
      }

      return true;
    },
  };

  return (
    <Upload {...uploadProps}>
      <Button
        type={buttonType}
        icon={loading ? <LoadingOutlined /> : <UploadOutlined />}
        loading={loading}
        disabled={disabled}
      >
        {loading ? '上传中...' : buttonText}
      </Button>
    </Upload>
  );
};

export default UploadButton;
