import { logout } from '@/utils/auth';
import { LogoutOutlined, UserOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Avatar, Dropdown, Space, Typography, message } from 'antd';
import React from 'react';

const { Text } = Typography;

const UserInfo: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  if (!currentUser) {
    return (
      <Space style={{ padding: '8px 12px' }}>
        <Avatar size="small" icon={<UserOutlined />} />
        <Text>未登录</Text>
      </Space>
    );
  }

  const handleLogout = async () => {
    try {
      message.success('正在退出登录...');
      await logout();
      // logout函数会处理页面跳转和状态清除
    } catch (error) {
      console.error('退出登录失败:', error);
      message.error('退出登录失败');
    }
  };

  const menuItems = [
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  return (
    <Dropdown menu={{ items: menuItems }} placement="bottomRight">
      <Space
        style={{
          cursor: 'pointer',
          padding: '8px 12px',
          borderRadius: '6px',
          transition: 'background-color 0.3s',
        }}
        className="user-info-trigger"
      >
        <Avatar
          size="small"
          style={{
            backgroundColor: '#1890ff',
          }}
        >
          {currentUser.username.charAt(0).toUpperCase()}
        </Avatar>
        <Text style={{ fontSize: '14px' }}>{currentUser.username}</Text>
      </Space>
    </Dropdown>
  );
};

export default UserInfo;
