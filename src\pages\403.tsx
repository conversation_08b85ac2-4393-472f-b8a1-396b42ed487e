import { history, useModel } from '@umijs/max';
import { Button, Result } from 'antd';
import React from 'react';

const NoPermission: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const handleBackHome = () => {
    if (currentUser) {
      // 已登录用户返回仪表盘
      history.push('/admin/dashboard');
    } else {
      // 未登录用户返回首页
      history.push('/');
    }
  };

  const handleLogin = () => {
    history.push('/admin/login');
  };

  return (
    <Result
      status="403"
      title="403"
      subTitle={
        currentUser
          ? `抱歉，您当前的角色（${
              currentUser.role === 'admin' ? '管理员' : '编辑员'
            }）没有权限访问此页面。`
          : '抱歉，您需要登录后才能访问此页面。'
      }
      extra={
        currentUser ? (
          <Button type="primary" onClick={handleBackHome}>
            返回首页
          </Button>
        ) : (
          <div>
            <Button
              type="primary"
              onClick={handleLogin}
              style={{ marginRight: 8 }}
            >
              立即登录
            </Button>
            <Button onClick={handleBackHome}>返回首页</Button>
          </div>
        )
      }
    />
  );
};

export default NoPermission;
