/**
 * @file 字典数据表格组件
 * @description 展示字典数据的表格组件，支持行选择、加载状态、操作回调等功能
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

import { Table } from 'antd';
import React from 'react';
import { TABLE_CONFIG } from '../constants';
import type { DictItem, DictType } from '../dict-types';
import { useDictTableColumns } from './DictTableColumns';

interface DictTableProps {
  type: DictType;
  data: DictItem[];
  loading: boolean;
  selectedRowKeys: React.Key[];
  onSelectChange: (selectedRowKeys: React.Key[]) => void;
  onEdit: (record: DictItem, type: DictType) => void;
  onDelete: (id: number, type: DictType) => void;
  onStatusToggle: (id: number, type: DictType) => void;
}

export const DictTable: React.FC<DictTableProps> = ({
  type,
  data,
  loading,
  selectedRowKeys,
  onSelectChange,
  onEdit,
  onDelete,
  onStatusToggle,
}) => {
  const columns = useDictTableColumns({
    type,
    onEdit,
    onDelete,
    onStatusToggle,
  });

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  return (
    <Table
      columns={columns}
      dataSource={data}
      rowKey="id"
      loading={loading}
      rowSelection={rowSelection}
      {...TABLE_CONFIG}
    />
  );
};
