/**
 * @file 字典数据管理Hook
 * @description 管理字典数据的加载、缓存和状态，包括区域、类型、关系三种字典的数据操作
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

import {
  getAllRegionDict,
  getAllRelationshipDict,
  getAllTypeDict,
  getRegionDictTree,
  getRelationshipDictTree,
  getTypeDictTree,
} from '@/services/dictionary';
import { message } from 'antd';
import { useCallback, useState } from 'react';
import { MESSAGES } from '../constants';
import type {
  DictType,
  RegionDict,
  RelationshipDict,
  TreeSelectData,
  TypeDict,
} from '../dict-types';

export const useDictData = () => {
  const [regionData, setRegionData] = useState<RegionDict[]>([]);
  const [typeData, setTypeData] = useState<TypeDict[]>([]);
  const [relationData, setRelationData] = useState<RelationshipDict[]>([]);
  const [loading, setLoading] = useState(false);
  const [parentOptions, setParentOptions] = useState<TreeSelectData[]>([]);

  // 加载区域字典数据
  const loadRegionData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getRegionDictTree();
      if (response.errCode === 0 && response.data) {
        setRegionData(response.data);
      }
    } catch (error) {
      message.error(MESSAGES.loadError.region);
    } finally {
      setLoading(false);
    }
  }, []);

  // 加载类型字典数据
  const loadTypeData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getTypeDictTree();
      if (response.errCode === 0 && response.data) {
        setTypeData(response.data);
      }
    } catch (error) {
      message.error(MESSAGES.loadError.type);
    } finally {
      setLoading(false);
    }
  }, []);

  // 加载关系字典数据
  const loadRelationData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getRelationshipDictTree();
      if (response.errCode === 0 && response.data) {
        setRelationData(response.data);
      }
    } catch (error) {
      message.error(MESSAGES.loadError.relation);
    } finally {
      setLoading(false);
    }
  }, []);

  // 根据类型加载数据
  const loadData = useCallback(
    (type: DictType) => {
      switch (type) {
        case 'region':
          loadRegionData();
          break;
        case 'type':
          loadTypeData();
          break;
        case 'relation':
          loadRelationData();
          break;
        default:
          break;
      }
    },
    [loadRegionData, loadTypeData, loadRelationData],
  );

  // 加载父级选项数据
  const loadParentOptions = useCallback(
    async (type: DictType, excludeId?: number) => {
      try {
        let response;
        switch (type) {
          case 'region':
            response = await getAllRegionDict();
            break;
          case 'type':
            response = await getAllTypeDict();
            break;
          case 'relation':
            response = await getAllRelationshipDict();
            break;
          default:
            return;
        }

        if (response.errCode === 0 && response.data) {
          // 转换为TreeSelect需要的格式，并排除当前编辑的项（避免循环引用）
          const convertToTreeSelectData = (items: any[]): TreeSelectData[] => {
            return items
              .filter((item) => item.id !== excludeId) // 排除当前编辑的项
              .map((item) => ({
                title: `${
                  item[
                    type === 'region'
                      ? 'regionName'
                      : type === 'type'
                      ? 'typeName'
                      : 'relationName'
                  ]
                } (${
                  item[
                    type === 'region'
                      ? 'regionCode'
                      : type === 'type'
                      ? 'typeCode'
                      : 'relationCode'
                  ]
                })`,
                value: item.id,
                key: item.id,
                children: item.children
                  ? convertToTreeSelectData(item.children)
                  : undefined,
              }));
          };

          setParentOptions(convertToTreeSelectData(response.data));
        }
      } catch (error) {
        console.error('加载父级选项失败:', error);
      }
    },
    [],
  );

  // 获取当前类型的数据
  const getCurrentData = useCallback(
    (type: DictType) => {
      switch (type) {
        case 'region':
          return regionData;
        case 'type':
          return typeData;
        case 'relation':
          return relationData;
        default:
          return [];
      }
    },
    [regionData, typeData, relationData],
  );

  return {
    // 数据状态
    regionData,
    typeData,
    relationData,
    loading,
    parentOptions,

    // 方法
    loadData,
    loadParentOptions,
    getCurrentData,

    // 单独的加载方法
    loadRegionData,
    loadTypeData,
    loadRelationData,
  };
};
