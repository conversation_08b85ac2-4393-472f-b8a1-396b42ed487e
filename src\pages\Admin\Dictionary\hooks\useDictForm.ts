/**
 * @file 字典表单管理Hook
 * @description 管理字典表单的状态、模态框显示、表单验证等功能
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

import { Form } from 'antd';
import { useCallback, useState } from 'react';
import { FORM_DEFAULT_VALUES } from '../constants';
import type { DictItem, DictType } from '../dict-types';

export const useDictForm = () => {
  const [form] = Form.useForm();
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<DictItem | null>(null);
  const [currentTab, setCurrentTab] = useState<DictType>('region');

  // 打开添加模态框
  const openAddModal = useCallback(
    (type: DictType) => {
      setEditingItem(null);
      setCurrentTab(type);
      form.resetFields();
      form.setFieldsValue(FORM_DEFAULT_VALUES);
      setModalVisible(true);
    },
    [form],
  );

  // 打开编辑模态框
  const openEditModal = useCallback(
    (record: DictItem, type: DictType) => {
      setEditingItem(record);
      setCurrentTab(type);
      form.setFieldsValue({
        ...record,
        parentId: record.parentId || null,
      });
      setModalVisible(true);
    },
    [form],
  );

  // 关闭模态框
  const closeModal = useCallback(() => {
    setModalVisible(false);
    setEditingItem(null);
    form.resetFields();
  }, [form]);

  // 获取模态框标题
  const getModalTitle = useCallback(() => {
    const action = editingItem ? '编辑' : '添加';
    switch (currentTab) {
      case 'region':
        return `${action}区域字典`;
      case 'type':
        return `${action}类型字典`;
      case 'relation':
        return `${action}关系字典`;
      default:
        return `${action}字典`;
    }
  }, [editingItem, currentTab]);

  // 验证表单并获取值
  const validateAndGetValues = useCallback(async () => {
    try {
      const values = await form.validateFields();
      return { success: true, values };
    } catch (error) {
      return { success: false, error };
    }
  }, [form]);

  return {
    form,
    modalVisible,
    editingItem,
    currentTab,
    setCurrentTab,
    openAddModal,
    openEditModal,
    closeModal,
    getModalTitle,
    validateAndGetValues,
  };
};
