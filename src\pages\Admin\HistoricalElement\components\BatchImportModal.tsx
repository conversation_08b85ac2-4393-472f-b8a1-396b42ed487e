import { Form, Input, Modal } from 'antd';
import React, { useEffect } from 'react';

const { TextArea } = Input;

export interface BatchImportModalProps {
  visible: boolean;
  loading: boolean;
  onOk: (importData: string) => void;
  onCancel: () => void;
}

export const BatchImportModal: React.FC<BatchImportModalProps> = ({
  visible,
  loading,
  onOk,
  onCancel,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [visible, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onOk(values.importData);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="批量导入历史要素"
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={800}
      confirmLoading={loading}
      destroyOnHidden
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="importData"
          label="导入数据"
          rules={[{ required: true, message: '请输入导入数据' }]}
          extra={
            <div>
              <p>请输入JSON格式的历史要素数据数组，示例格式：</p>
              <pre
                style={{
                  backgroundColor: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  overflow: 'auto',
                }}
              >
                {`[
                  {
                    "name": "大雁塔",
                    "code": "DYT001",
                    "typeDictId": 1,
                    "constructionLongitude": 108.9640,
                    "constructionLatitude": 34.2180,
                    "locationDescription": "位于西安市雁塔区大慈恩寺内",
                    "constructionTime": "652-01-01T00:00:00.000Z",
                    "historicalRecords": "大雁塔又名慈恩寺塔...",
                    "regionDictId": 1
                  }
                ]`}
              </pre>
            </div>
          }
        >
          <TextArea
            rows={10}
            placeholder="请输入JSON格式的历史要素数据..."
            style={{ fontFamily: 'monospace' }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};
