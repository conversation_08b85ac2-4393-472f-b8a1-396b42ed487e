import { But<PERSON>, <PERSON>, Col, Row, Space } from 'antd';
import React from 'react';

export interface BatchOperationBarProps {
  selectedCount: number;
  batchLoading: boolean;
  onClearSelection: () => void;
  onBatchDelete: () => void;
}

export const BatchOperationBar: React.FC<BatchOperationBarProps> = ({
  selectedCount,
  batchLoading,
  onClearSelection,
  onBatchDelete,
}) => {
  if (selectedCount === 0) {
    return null;
  }

  return (
    <Card style={{ marginBottom: 16, backgroundColor: '#f0f2f5' }}>
      <Row justify="space-between" align="middle">
        <Col>
          <Space>
            <span>已选择 {selectedCount} 项</span>
            <Button size="small" onClick={onClearSelection}>
              取消选择
            </Button>
          </Space>
        </Col>
        <Col>
          <Space>
            <Button
              size="small"
              danger
              loading={batchLoading}
              onClick={onBatchDelete}
            >
              批量删除
            </Button>
          </Space>
        </Col>
      </Row>
    </Card>
  );
};
