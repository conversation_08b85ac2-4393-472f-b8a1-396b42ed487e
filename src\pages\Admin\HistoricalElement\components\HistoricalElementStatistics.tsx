import { Card, Col, Row, Statistic } from 'antd';
import React from 'react';
import type { HistoricalElementStatistics as StatisticsType } from '@/services/historicalElement';

export interface HistoricalElementStatisticsProps {
  statistics: StatisticsType | null;
  loading: boolean;
}

export const HistoricalElementStatistics: React.FC<HistoricalElementStatisticsProps> = ({
  statistics,
  loading,
}) => {
  // 渲染统计卡片
  const renderStatisticsCards = () => {
    if (!statistics) return null;
    
    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic title="总数量" value={statistics.total} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="类型数量" value={statistics.byType.length} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="区域数量" value={statistics.byRegion.length} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="时期数量" value={statistics.byPeriod.length} />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染统计详情
  const renderStatisticsDetails = () => {
    if (!statistics) return null;
    
    return (
      <Row gutter={16}>
        <Col span={8}>
          <Card title="按类型统计" loading={loading}>
            {statistics.byType.map(item => (
              <div key={item.typeId} style={{ marginBottom: 8 }}>
                <span>{item.typeName}: </span>
                <span style={{ fontWeight: 'bold' }}>{item.count}</span>
              </div>
            ))}
          </Card>
        </Col>
        <Col span={8}>
          <Card title="按区域统计" loading={loading}>
            {statistics.byRegion.map(item => (
              <div key={item.regionId} style={{ marginBottom: 8 }}>
                <span>{item.regionName}: </span>
                <span style={{ fontWeight: 'bold' }}>{item.count}</span>
              </div>
            ))}
          </Card>
        </Col>
        <Col span={8}>
          <Card title="按时期统计" loading={loading}>
            {statistics.byPeriod.map(item => (
              <div key={item.period} style={{ marginBottom: 8 }}>
                <span>{item.period}: </span>
                <span style={{ fontWeight: 'bold' }}>{item.count}</span>
              </div>
            ))}
          </Card>
        </Col>
      </Row>
    );
  };

  return (
    <div>
      {renderStatisticsCards()}
      {renderStatisticsDetails()}
    </div>
  );
};
