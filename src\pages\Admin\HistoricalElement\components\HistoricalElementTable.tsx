import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Popconfirm, Space, Table } from 'antd';
import dayjs from 'dayjs';
import React from 'react';
import type { HistoricalElement } from '@/services/historicalElement';

export interface HistoricalElementTableProps {
  data: HistoricalElement[];
  loading: boolean;
  selectedRowKeys: React.Key[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  onSelectChange: (selectedRowKeys: React.Key[]) => void;
  onEdit: (record: HistoricalElement) => void;
  onDelete: (id: number) => void;
  onPaginationChange: (page: number, pageSize: number) => void;
  onShowSizeChange: (current: number, size: number) => void;
}

export const HistoricalElementTable: React.FC<HistoricalElementTableProps> = ({
  data,
  loading,
  selectedRowKeys,
  pagination,
  onSelectChange,
  onEdit,
  onDelete,
  onPaginationChange,
  onShowSizeChange,
}) => {
  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '类型',
      dataIndex: 'typeDictId',
      key: 'typeDictId',
      render: (_: any, record: HistoricalElement) => {
        return record.typeDict?.typeName || '未知类型';
      },
    },
    {
      title: '建造时间',
      dataIndex: 'constructionTime',
      key: 'constructionTime',
      render: (time: string) => time ? dayjs(time).format('YYYY年MM月DD日') : '-',
    },
    {
      title: '所属区域',
      dataIndex: 'regionDictId',
      key: 'regionDictId',
      render: (_: any, record: HistoricalElement) => {
        return record.regionDict?.regionName || '未知区域';
      },
    },
    {
      title: '位置',
      key: 'location',
      render: (_: any, record: HistoricalElement) => (
        <span>
          {record.constructionLongitude.toFixed(4)}, {record.constructionLatitude.toFixed(4)}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: HistoricalElement) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => onEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => onDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={data}
      rowKey="id"
      loading={loading}
      rowSelection={{
        selectedRowKeys,
        onChange: onSelectChange,
      }}
      pagination={{
        ...pagination,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `第 ${range?.[0]}-${range?.[1]} 条，共 ${total} 条记录`,
        onChange: onPaginationChange,
        onShowSizeChange,
      }}
    />
  );
};
