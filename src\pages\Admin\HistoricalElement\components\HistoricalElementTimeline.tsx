import type { TimelineData } from '@/services/historicalElement';
import { Card, Timeline } from 'antd';
import React from 'react';

export interface HistoricalElementTimelineProps {
  timelineData: TimelineData[];
  loading: boolean;
}

export const HistoricalElementTimeline: React.FC<
  HistoricalElementTimelineProps
> = ({ timelineData, loading }) => {
  return (
    <Card title="历史要素时间轴" loading={loading}>
      <Timeline mode="left">
        {timelineData.map((item) => (
          <Timeline.Item key={item.year} label={`${item.year}年`}>
            <div>
              <div style={{ fontWeight: 'bold', marginBottom: 8 }}>
                共 {item.count} 个历史要素
              </div>
              {item.elements.map((element) => (
                <div key={element.id} style={{ marginBottom: 4 }}>
                  • {element.name}
                </div>
              ))}
            </div>
          </Timeline.Item>
        ))}
      </Timeline>
    </Card>
  );
};
