import { message } from 'antd';
import { useCallback, useState } from 'react';
import {
  getHistoricalElementList,
  getHistoricalElementStatistics,
  getHistoricalElementTimeline,
  type HistoricalElement,
  type HistoricalElementStatistics,
  type TimelineData,
} from '@/services/historicalElement';

export interface UseHistoricalElementDataReturn {
  // 数据状态
  data: HistoricalElement[];
  statistics: HistoricalElementStatistics | null;
  timelineData: TimelineData[];
  
  // 加载状态
  loading: boolean;
  statisticsLoading: boolean;
  timelineLoading: boolean;
  
  // 分页状态
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  
  // 数据获取函数
  fetchData: (page?: number, pageSize?: number, keyword?: string, regionId?: number, typeId?: number) => Promise<void>;
  fetchStatistics: (regionId?: number, typeId?: number) => Promise<void>;
  fetchTimelineData: (regionId?: number) => Promise<void>;
  
  // 状态更新函数
  setPagination: React.Dispatch<React.SetStateAction<{
    current: number;
    pageSize: number;
    total: number;
  }>>;
}

export const useHistoricalElementData = (): UseHistoricalElementDataReturn => {
  // 数据状态
  const [data, setData] = useState<HistoricalElement[]>([]);
  const [statistics, setStatistics] = useState<HistoricalElementStatistics | null>(null);
  const [timelineData, setTimelineData] = useState<TimelineData[]>([]);
  
  // 加载状态
  const [loading, setLoading] = useState(false);
  const [statisticsLoading, setStatisticsLoading] = useState(false);
  const [timelineLoading, setTimelineLoading] = useState(false);
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取历史要素列表
  const fetchData = useCallback(async (
    page = 1,
    pageSize = 10,
    keyword?: string,
    regionId?: number,
    typeId?: number,
  ) => {
    setLoading(true);
    try {
      const response = await getHistoricalElementList({
        page,
        pageSize,
        keyword: keyword || undefined,
        regionId,
        typeId,
      });

      if (response.errCode === 0 && response.data) {
        setData(response.data.list);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      } else {
        message.error(response.msg || '获取历史要素列表失败');
      }
    } catch (error: any) {
      console.error('获取历史要素列表失败:', error);
      message.error(error?.response?.data?.msg || '获取历史要素列表失败');
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取统计数据
  const fetchStatistics = useCallback(async (regionId?: number, typeId?: number) => {
    setStatisticsLoading(true);
    try {
      const response = await getHistoricalElementStatistics({
        regionId,
        typeId,
      });
      
      if (response.errCode === 0 && response.data) {
        setStatistics(response.data);
      } else {
        message.error(response.msg || '获取统计数据失败');
      }
    } catch (error: any) {
      console.error('获取统计数据失败:', error);
      message.error(error?.response?.data?.msg || '获取统计数据失败');
    } finally {
      setStatisticsLoading(false);
    }
  }, []);

  // 获取时间轴数据
  const fetchTimelineData = useCallback(async (regionId?: number) => {
    setTimelineLoading(true);
    try {
      const response = await getHistoricalElementTimeline({
        regionId,
      });
      
      if (response.errCode === 0 && response.data) {
        setTimelineData(response.data);
      } else {
        message.error(response.msg || '获取时间轴数据失败');
      }
    } catch (error: any) {
      console.error('获取时间轴数据失败:', error);
      message.error(error?.response?.data?.msg || '获取时间轴数据失败');
    } finally {
      setTimelineLoading(false);
    }
  }, []);

  return {
    // 数据状态
    data,
    statistics,
    timelineData,
    
    // 加载状态
    loading,
    statisticsLoading,
    timelineLoading,
    
    // 分页状态
    pagination,
    
    // 数据获取函数
    fetchData,
    fetchStatistics,
    fetchTimelineData,
    
    // 状态更新函数
    setPagination,
  };
};
