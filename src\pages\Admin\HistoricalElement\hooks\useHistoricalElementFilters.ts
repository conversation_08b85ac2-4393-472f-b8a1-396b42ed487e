import { useCallback, useState } from 'react';

export interface UseHistoricalElementFiltersReturn {
  // 筛选状态
  searchKeyword: string;
  regionFilter: number | undefined;
  typeFilter: number | undefined;
  selectedRowKeys: React.Key[];
  
  // 状态更新函数
  setSearchKeyword: React.Dispatch<React.SetStateAction<string>>;
  setRegionFilter: React.Dispatch<React.SetStateAction<number | undefined>>;
  setTypeFilter: React.Dispatch<React.SetStateAction<number | undefined>>;
  setSelectedRowKeys: React.Dispatch<React.SetStateAction<React.Key[]>>;
  
  // 操作函数
  handleSearch: (value: string) => void;
  handleRegionFilterChange: (value: number | undefined) => void;
  handleTypeFilterChange: (value: number | undefined) => void;
  handleSelectChange: (newSelectedRowKeys: React.Key[]) => void;
  clearSelection: () => void;
  resetFilters: () => void;
  
  // 工具函数
  checkSelection: () => boolean;
  getSelectedIds: () => number[];
}

export const useHistoricalElementFilters = (): UseHistoricalElementFiltersReturn => {
  // 筛选状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [regionFilter, setRegionFilter] = useState<number | undefined>();
  const [typeFilter, setTypeFilter] = useState<number | undefined>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 搜索处理
  const handleSearch = useCallback((value: string) => {
    setSearchKeyword(value);
  }, []);

  // 区域筛选处理
  const handleRegionFilterChange = useCallback((value: number | undefined) => {
    setRegionFilter(value);
  }, []);

  // 类型筛选处理
  const handleTypeFilterChange = useCallback((value: number | undefined) => {
    setTypeFilter(value);
  }, []);

  // 行选择处理
  const handleSelectChange = useCallback((newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  }, []);

  // 清空选择
  const clearSelection = useCallback(() => {
    setSelectedRowKeys([]);
  }, []);

  // 重置筛选条件
  const resetFilters = useCallback(() => {
    setSearchKeyword('');
    setRegionFilter(undefined);
    setTypeFilter(undefined);
    setSelectedRowKeys([]);
  }, []);

  // 检查是否有选中项
  const checkSelection = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      return false;
    }
    return true;
  }, [selectedRowKeys.length]);

  // 获取选中的ID数组
  const getSelectedIds = useCallback(() => {
    return selectedRowKeys.map((key) => Number(key));
  }, [selectedRowKeys]);

  return {
    // 筛选状态
    searchKeyword,
    regionFilter,
    typeFilter,
    selectedRowKeys,
    
    // 状态更新函数
    setSearchKeyword,
    setRegionFilter,
    setTypeFilter,
    setSelectedRowKeys,
    
    // 操作函数
    handleSearch,
    handleRegionFilterChange,
    handleTypeFilterChange,
    handleSelectChange,
    clearSelection,
    resetFilters,
    
    // 工具函数
    checkSelection,
    getSelectedIds,
  };
};
