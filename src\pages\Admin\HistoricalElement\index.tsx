import AdminLayout, { TypeOption } from '@/components/AdminLayout';
import { RegionTreeProvider } from '@/components/AdminLayout/hooks/useRegionTree';
import { BarChartOutlined, HistoryOutlined, PlusOutlined, TableOutlined } from '@ant-design/icons';
import { But<PERSON>, Space } from 'antd';
import React, { useState } from 'react';
import { BatchImportModal } from './components/BatchImportModal';
import { BatchOperationBar } from './components/BatchOperationBar';
import { HistoricalElementForm } from './components/HistoricalElementForm';
import { HistoricalElementStatistics } from './components/HistoricalElementStatistics';
import { HistoricalElementTable } from './components/HistoricalElementTable';
import { HistoricalElementTimeline } from './components/HistoricalElementTimeline';
import { HistoricalElementToolbar } from './components/HistoricalElementToolbar';
import { useHistoricalElementManager } from './hooks/useHistoricalElementManager';

const AdminHistoricalElement: React.FC = () => {
  const manager = useHistoricalElementManager();
  const [selectedRegion, setSelectedRegion] = useState<React.Key | undefined>();

  // 类型切换选项
  const typeOptions: TypeOption[] = [
    {
      label: '数据列表',
      value: 'list',
      icon: <TableOutlined />,
    },
    {
      label: '统计分析',
      value: 'statistics',
      icon: <BarChartOutlined />,
    },
    {
      label: '时间轴',
      value: 'timeline',
      icon: <HistoryOutlined />,
    },
  ];

  // 处理区域选择
  const handleRegionSelect = (selectedKeys: React.Key[]) => {
    const regionId = selectedKeys[0];
    setSelectedRegion(regionId);
    // 触发区域筛选
    manager.handleRegionFilterChange(regionId as number);
  };

  // 渲染右侧内容
  const renderContent = () => {
    switch (manager.activeTab) {
      case 'list':
        return (
          <div>
            {/* 搜索和筛选工具栏 */}
            <HistoricalElementToolbar
              searchKeyword={manager.searchKeyword}
              regionFilter={manager.regionFilter}
              typeFilter={manager.typeFilter}
              onSearchKeywordChange={(value) => manager.handleSearch(value)}
              onSearch={manager.handleSearch}
              onRegionFilterChange={manager.handleRegionFilterChange}
              onTypeFilterChange={manager.handleTypeFilterChange}
              onRefresh={manager.handleRefresh}
              onReset={manager.handleReset}
            />

            {/* 批量操作栏 */}
            <BatchOperationBar
              selectedCount={manager.selectedRowKeys.length}
              batchLoading={manager.batchLoading}
              onClearSelection={manager.clearSelection}
              onBatchDelete={manager.handleBatchDelete}
            />

            {/* 数据表格 */}
            <HistoricalElementTable
              data={manager.data}
              loading={manager.loading}
              selectedRowKeys={manager.selectedRowKeys}
              pagination={manager.pagination}
              onSelectChange={manager.handleSelectChange}
              onEdit={manager.handleEdit}
              onDelete={manager.handleDelete}
              onPaginationChange={manager.handlePaginationChange}
              onShowSizeChange={manager.handleShowSizeChange}
            />
          </div>
        );
      case 'statistics':
        return (
          <HistoricalElementStatistics
            statistics={manager.statistics}
            loading={manager.statisticsLoading}
          />
        );
      case 'timeline':
        return (
          <HistoricalElementTimeline
            timelineData={manager.timelineData}
            loading={manager.timelineLoading}
          />
        );
      default:
        return null;
    }
  };

  return (
    <RegionTreeProvider>
      {({ treeData }) => (
        <>
          <AdminLayout
            title="历史要素管理"
            regionTreeData={treeData}
            selectedRegion={selectedRegion}
            onRegionSelect={handleRegionSelect}
            typeOptions={typeOptions}
            selectedType={manager.activeTab}
            onTypeChange={manager.handleTabChange}
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={manager.handleAdd}
                >
                  添加历史要素
                </Button>
                <Button onClick={manager.handleImportModalOpen}>批量导入</Button>
              </Space>
            }
          >
            {renderContent()}
          </AdminLayout>

          {/* 添加/编辑表单模态框 */}
          <HistoricalElementForm
            visible={manager.modalVisible}
            loading={manager.operationLoading}
            editingItem={manager.editingItem}
            onOk={manager.handleSubmit}
            onCancel={manager.handleModalCancel}
          />

          {/* 批量导入模态框 */}
          <BatchImportModal
            visible={manager.importModalVisible}
            loading={manager.batchLoading}
            onOk={manager.handleBatchImport}
            onCancel={manager.handleImportModalClose}
          />
        </>
      )}
    </RegionTreeProvider>
  );
};

export default AdminHistoricalElement;
