/**
 * @file 山塬管理页面
 * @description 使用通用AdminLayout布局的山塬管理界面
 * <AUTHOR> Assistant
 * @date 2025-08-30
 */

import AdminLayout, { TypeOption } from '@/components/AdminLayout';
import { RegionTreeProvider } from '@/components/AdminLayout/hooks/useRegionTree';
import { BarChartOutlined, HistoryOutlined, PlusOutlined, TableOutlined } from '@ant-design/icons';
import { Button, Space, Table, Card, Input, Select } from 'antd';
import React, { useState } from 'react';

const { Search } = Input;
const { Option } = Select;

const AdminPlateau: React.FC = () => {
  const [selectedRegion, setSelectedRegion] = useState<React.Key | undefined>();
  const [activeTab, setActiveTab] = useState('list');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [statusFilter, setStatusFilter] = useState<number | undefined>();

  // 类型切换选项
  const typeOptions: TypeOption[] = [
    {
      label: '数据列表',
      value: 'list',
      icon: <TableOutlined />,
    },
    {
      label: '统计分析',
      value: 'statistics',
      icon: <BarChartOutlined />,
    },
    {
      label: '时间轴',
      value: 'timeline',
      icon: <HistoryOutlined />,
    },
  ];

  // 处理区域选择
  const handleRegionSelect = (selectedKeys: React.Key[]) => {
    const regionId = selectedKeys[0];
    setSelectedRegion(regionId);
    console.log('选择的区域ID:', regionId);
  };

  // 模拟山塬数据
  const mockPlateauData = [
    {
      id: 1,
      name: '黄土高原',
      code: 'HT001',
      area: 1200.5,
      elevation: 1500,
      regionName: '陕西省',
      status: 1,
    },
    {
      id: 2,
      name: '关中平原',
      code: 'GZ001',
      area: 800.3,
      elevation: 400,
      regionName: '西安市',
      status: 1,
    },
  ];

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '面积(km²)',
      dataIndex: 'area',
      key: 'area',
    },
    {
      title: '海拔(m)',
      dataIndex: 'elevation',
      key: 'elevation',
    },
    {
      title: '所属区域',
      dataIndex: 'regionName',
      key: 'regionName',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: () => (
        <Space size="middle">
          <Button type="link">编辑</Button>
          <Button type="link" danger>删除</Button>
        </Space>
      ),
    },
  ];

  // 渲染右侧内容
  const renderContent = () => {
    switch (activeTab) {
      case 'list':
        return (
          <div>
            {/* 搜索工具栏 */}
            <Card style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', gap: 16, alignItems: 'center' }}>
                <Search
                  placeholder="搜索山塬名称"
                  allowClear
                  style={{ width: 300 }}
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                />
                <Select
                  placeholder="状态筛选"
                  allowClear
                  style={{ width: 120 }}
                  value={statusFilter}
                  onChange={setStatusFilter}
                >
                  <Option value={1}>启用</Option>
                  <Option value={0}>禁用</Option>
                </Select>
                <Button>刷新</Button>
                <Button>重置</Button>
              </div>
            </Card>

            {/* 数据表格 */}
            <Table
              columns={columns}
              dataSource={mockPlateauData}
              rowKey="id"
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range?.[0]}-${range?.[1]} 条，共 ${total} 条记录`,
              }}
            />
          </div>
        );
      case 'statistics':
        return (
          <Card>
            <h3>山塬统计分析</h3>
            <p>这里显示山塬的统计图表和分析数据...</p>
          </Card>
        );
      case 'timeline':
        return (
          <Card>
            <h3>山塬时间轴</h3>
            <p>这里显示山塬的历史变迁时间轴...</p>
          </Card>
        );
      default:
        return null;
    }
  };

  return (
    <RegionTreeProvider>
      {({ treeData }) => (
        <AdminLayout
          title="山塬管理"
          regionTreeData={treeData}
          selectedRegion={selectedRegion}
          onRegionSelect={handleRegionSelect}
          typeOptions={typeOptions}
          selectedType={activeTab}
          onTypeChange={setActiveTab}
          extra={
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
              >
                添加山塬
              </Button>
              <Button>批量导入</Button>
            </Space>
          }
        >
          {renderContent()}
        </AdminLayout>
      )}
    </RegionTreeProvider>
  );
};

export default AdminPlateau;
