# 资源管理页面

## 概述

资源管理页面提供了完整的文件上传和管理功能，支持图片文件的上传、预览、编辑和删除。该页面已根据后端 API 文档进行了完整的功能实现。

## 功能特性

### 📤 文件上传

- **拖拽上传**：支持将文件拖拽到上传区域
- **点击上传**：点击上传区域选择文件

- **实时进度**：显示每个文件的上传进度
- **文件验证**：自动验证文件类型和大小

### 🖼️ 支持的文件格式

- JPG / JPEG
- PNG
- GIF
- BMP
- WEBP

### 📊 统计信息

- 总文件数量
- 关联山塬的文件数
- 关联水系的文件数
- 关联历史要素的文件数

### 📋 文件管理

- **文件列表**：表格形式展示所有上传的文件
- **文件预览**：点击预览图可查看大图
- **文件编辑**：编辑文件名称和关联对象
- **文件删除**：删除不需要的文件
- **文件链接**：点击文件路径可直接访问文件

## API 集成

### 上传接口

#### 单文件上传

- **接口**: `POST /api/upload/file`
- **参数**: `file` (FormData)
- **响应**:

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "url": "/public/uploads/2024/01/15/image_1642234567890.jpg",
    "filename": "image.jpg",
    "size": 1024000
  }
}
```

#### 多文件上传

- **接口**: `POST /api/upload/files`
- **参数**: `files` (FormData[])
- **响应**:

```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "url": "/public/uploads/2024/01/15/image1_1642234567890.jpg",
      "filename": "image1.jpg",
      "size": 1024000
    }
  ]
}
```

### 文件限制

- **文件大小**: 最大 50MB
- **文件类型**: 仅支持图片格式
- **认证要求**: 需要有效的 JWT Token

## 技术实现

### 核心组件

- `src/services/upload.ts` - 上传服务和工具函数
- `src/pages/Admin/Upload/index.tsx` - 主要组件
- `src/pages/Admin/Upload/index.less` - 样式文件

### 关键功能

#### 文件验证

```typescript
const validation = uploadUtils.validateFile(file);
if (!validation.valid) {
  message.error(validation.message);
  return;
}
```

#### 自定义上传

```typescript
const customUpload = async (options: any) => {
  const { file, onSuccess, onError, onProgress } = options;
  // 文件验证、上传进度、错误处理
};
```

```

## 使用说明

### 1. 单文件上传
1. 点击上传区域或拖拽文件到上传区域
2. 系统自动验证文件类型和大小
3. 显示上传进度
4. 上传成功后文件自动添加到列表

### 2. 文件管理
1. 在文件列表中查看所有上传的文件
2. 点击"编辑"按钮修改文件信息
3. 选择关联的山塬、水系或历史要素
4. 点击"删除"按钮移除文件

### 4. 文件预览
1. 点击预览图查看大图
2. 支持图片缩放和旋转
3. 点击文件路径直接访问文件

## 错误处理

### 常见错误
- **文件类型不支持**: 仅支持图片格式文件
- **文件过大**: 单个文件不能超过 50MB
- **网络错误**: 检查网络连接和服务器状态
- **认证失败**: 确保已正确登录

### 错误提示
系统会显示详细的错误信息，帮助用户了解问题原因：
- 文件验证失败时显示具体原因
- 上传失败时显示错误信息
- 网络问题时提示重试

## 开发调试

### 测试工具
在开发环境下，页面会自动运行测试工具：
- 文件验证功能测试
- 文件大小格式化测试
- 文件扩展名获取测试

### 调试信息
打开浏览器控制台可以看到：
- 上传过程的详细日志
- 文件验证结果
- API 请求和响应信息

## 样式定制

### CSS 类名
- `.upload-container` - 主容器
- `.stats-cards` - 统计卡片区域
- `.upload-progress` - 上传进度区域
- `.file-table` - 文件列表表格
- `.upload-tips` - 上传提示区域

### 响应式设计
页面支持移动端适配，在小屏幕设备上会自动调整布局。

## 注意事项

1. **文件安全**: 仅支持图片格式，防止恶意文件上传
2. **大小限制**: 50MB 限制确保服务器性能
3. **认证要求**: 所有上传操作都需要登录认证
4. **存储路径**: 文件按日期自动分类存储
5. **文件命名**: 系统自动添加时间戳避免重名

## 后续优化

- [ ] 支持更多文件格式
- [ ] 添加文件压缩功能
- [ ] 支持文件夹上传
- [ ] 添加上传历史记录
- [ ] 支持文件标签和分类
- [ ] 添加文件搜索功能
```
