.upload-container {
  .ant-upload-drag {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    background: #fafafa;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1890ff;
      background: #f0f8ff;
    }

    &.ant-upload-drag-hover {
      border-color: #1890ff;
      background: #f0f8ff;
    }
  }

  .ant-upload-drag-icon {
    .anticon {
      font-size: 48px;
      color: #1890ff;
    }
  }

  .ant-upload-text {
    font-size: 16px;
    color: #666;
    margin: 16px 0 8px;
  }

  .ant-upload-hint {
    color: #999;
    font-size: 14px;
  }

  .upload-progress {
    margin-top: 16px;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 6px;

    .progress-item {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .file-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        font-size: 14px;

        .file-name {
          color: #333;
          font-weight: 500;
        }

        .file-size {
          color: #999;
        }
      }
    }
  }

  .stats-cards {
    .ant-card {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 10%);
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 15%);
        transform: translateY(-2px);
      }

      .ant-card-body {
        padding: 16px;
      }
    }

    .stat-number {
      font-size: 24px;
      font-weight: bold;
      line-height: 1;
      margin-bottom: 4px;
    }

    .stat-label {
      color: #666;
      font-size: 12px;
    }
  }

  .file-table {
    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 600;
    }

    .ant-image {
      border-radius: 4px;
      overflow: hidden;
    }

    .file-url {
      color: #1890ff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .upload-tips {
    margin-top: 16px;
    padding: 12px;
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 6px;

    .tips-title {
      font-weight: 600;
      color: #389e0d;
      margin-bottom: 8px;
    }

    .tips-list {
      margin: 0;
      padding-left: 16px;
      color: #52c41a;

      li {
        margin-bottom: 4px;
        font-size: 14px;
      }
    }
  }

  .batch-upload-btn {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(24, 144, 255, 70%);
    }

    70% {
      box-shadow: 0 0 0 10px rgba(24, 144, 255, 0%);
    }

    100% {
      box-shadow: 0 0 0 0 rgba(24, 144, 255, 0%);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .upload-container {
    .stats-cards {
      .ant-space {
        width: 100%;

        .ant-space-item {
          width: 100%;
          margin-bottom: 8px;
        }
      }
    }

    .ant-table {
      font-size: 12px;
    }

    .upload-tips {
      .tips-list {
        padding-left: 12px;

        li {
          font-size: 12px;
        }
      }
    }
  }
}
