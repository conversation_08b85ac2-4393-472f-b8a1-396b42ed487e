import AdminLayout, { TypeOption } from '@/components/AdminLayout';
import { RegionTreeProvider } from '@/components/AdminLayout/hooks/useRegionTree';
import { regionDict, waterSystemData } from '@/services/mockData';
import { BarChartOutlined, DeleteOutlined, EditOutlined, HistoryOutlined, PlusOutlined, TableOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Select,
  Space,
  Table,
  Tag,
} from 'antd';
import React, { useState } from 'react';

const { TextArea, Search } = Input;
const { Option } = Select;

const AdminWaterSystem: React.FC = () => {
  const [data, setData] = useState(waterSystemData);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [form] = Form.useForm();
  const [selectedRegion, setSelectedRegion] = useState<React.Key | undefined>();
  const [activeTab, setActiveTab] = useState('list');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [typeFilter, setTypeFilter] = useState<string | undefined>();

  // 类型切换选项
  const typeOptions: TypeOption[] = [
    {
      label: '数据列表',
      value: 'list',
      icon: <TableOutlined />,
    },
    {
      label: '统计分析',
      value: 'statistics',
      icon: <BarChartOutlined />,
    },
    {
      label: '时间轴',
      value: 'timeline',
      icon: <HistoryOutlined />,
    },
  ];

  // 处理区域选择
  const handleRegionSelect = (selectedKeys: React.Key[]) => {
    const regionId = selectedKeys[0];
    setSelectedRegion(regionId);
    console.log('选择的区域ID:', regionId);
  };

  const handleAdd = () => {
    setEditingItem(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record: any) => {
    setEditingItem(record);
    form.setFieldsValue(record);
    setModalVisible(true);
  };

  const handleDelete = (id: number) => {
    setData(data.filter((item) => item.id !== id));
    message.success('删除成功！');
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (editingItem) {
        // 编辑
        setData(
          data.map((item) =>
            item.id === editingItem.id ? { ...item, ...values } : item,
          ),
        );
        message.success('编辑成功！');
      } else {
        // 新增
        const newId = Math.max(...data.map((item) => item.id)) + 1;
        setData([...data, { id: newId, ...values }]);
        message.success('添加成功！');
      }

      setModalVisible(false);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '经度',
      dataIndex: 'longitude',
      key: 'longitude',
    },
    {
      title: '纬度',
      dataIndex: 'latitude',
      key: 'latitude',
    },
    {
      title: '长度/面积',
      dataIndex: 'length_area',
      key: 'length_area',
    },
    {
      title: '所属区域',
      dataIndex: 'region_dict_id',
      key: 'region_dict_id',
      render: (regionId: number) => {
        const region = regionDict.find((r) => r.id === regionId);
        return region?.region_name || '未知区域';
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 渲染右侧内容
  const renderContent = () => {
    switch (activeTab) {
      case 'list':
        return (
          <div>
            {/* 搜索工具栏 */}
            <Card style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', gap: 16, alignItems: 'center' }}>
                <Search
                  placeholder="搜索水系名称"
                  allowClear
                  style={{ width: 300 }}
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                />
                <Select
                  placeholder="类型筛选"
                  allowClear
                  style={{ width: 120 }}
                  value={typeFilter}
                  onChange={setTypeFilter}
                >
                  <Option value="主干河流">主干河流</Option>
                  <Option value="支流">支流</Option>
                  <Option value="湖泊">湖泊</Option>
                </Select>
                <Button>刷新</Button>
                <Button>重置</Button>
              </div>
            </Card>

            {/* 数据表格 */}
            <Table
              columns={columns}
              dataSource={data}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </div>
        );
      case 'statistics':
        return (
          <Card>
            <h3>水系统计分析</h3>
            <p>这里显示水系的统计图表和分析数据...</p>
            <div style={{ marginTop: 20 }}>
              <p>• 水系总数: {data.length} 个</p>
              <p>• 覆盖区域: {new Set(data.map(item => item.region_dict_id)).size} 个</p>
            </div>
          </Card>
        );
      case 'timeline':
        return (
          <Card>
            <h3>水系时间轴</h3>
            <p>这里显示水系的历史变迁和重要事件时间轴...</p>
          </Card>
        );
      default:
        return null;
    }
  };

  return (
    <RegionTreeProvider>
      {({ treeData }) => (
        <>
          <AdminLayout
            title="水系管理"
            regionTreeData={treeData}
            selectedRegion={selectedRegion}
            onRegionSelect={handleRegionSelect}
            typeOptions={typeOptions}
            selectedType={activeTab}
            onTypeChange={setActiveTab}
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                >
                  添加水系
                </Button>
                <Button>批量导入</Button>
              </Space>
            }
          >
            {renderContent()}
          </AdminLayout>

          {/* 添加/编辑表单模态框 */}
          <Modal
            title={editingItem ? '编辑水系' : '添加水系'}
            open={modalVisible}
            onOk={handleSubmit}
            onCancel={() => setModalVisible(false)}
            width={600}
            destroyOnHidden
          >
            <Form form={form} layout="vertical">
              <Form.Item
                name="name"
                label="名称"
                rules={[{ required: true, message: '请输入水系名称' }]}
              >
                <Input placeholder="请输入水系名称" />
              </Form.Item>

              <Form.Item
                name="code"
                label="编号"
                rules={[{ required: true, message: '请输入水系编号' }]}
              >
                <Input placeholder="请输入水系编号" />
              </Form.Item>

              <Form.Item
                name="longitude"
                label="经度"
                rules={[{ required: true, message: '请输入经度' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入经度"
                  precision={6}
                  min={-180}
                  max={180}
                />
              </Form.Item>

              <Form.Item
                name="latitude"
                label="纬度"
                rules={[{ required: true, message: '请输入纬度' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入纬度"
                  precision={6}
                  min={-90}
                  max={90}
                />
              </Form.Item>

              <Form.Item
                name="length_area"
                label="长度/面积"
                rules={[{ required: true, message: '请输入长度或面积' }]}
              >
                <Input placeholder="请输入长度或面积，如：818公里" />
              </Form.Item>

              <Form.Item
                name="region_dict_id"
                label="所属区域"
                rules={[{ required: true, message: '请选择所属区域' }]}
              >
                <Select placeholder="请选择所属区域">
                  {regionDict.map((region) => (
                    <Select.Option key={region.id} value={region.id}>
                      {region.region_name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="historical_records"
                label="历史记载"
                rules={[{ required: true, message: '请输入历史记载' }]}
              >
                <TextArea rows={4} placeholder="请输入历史记载" />
              </Form.Item>
            </Form>
          </Modal>
        </>
      )}
    </RegionTreeProvider>
  );
};

export default AdminWaterSystem;
