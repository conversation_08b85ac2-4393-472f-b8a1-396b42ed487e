/* Hero Banner */
.hero-banner {
  position: relative;
  height: 80vh;
  min-height: 600px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  @media (max-width: 768px) {
    background-attachment: scroll;
  }

  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(102, 126, 234, 10%) 0%,
      rgba(118, 75, 162, 10%) 100%
    );
    z-index: 1;
  }

  .hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 0 24px;

    .hero-title {
      color: white !important;
      font-size: 3.5rem;
      font-weight: 700;
      margin-bottom: 24px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 30%);
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 2.5rem;
      }
    }

    .hero-description {
      font-size: 1.25rem;
      margin-bottom: 40px;
      color: rgba(255, 255, 255, 90%);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 30%);
      line-height: 1.6;

      @media (max-width: 768px) {
        font-size: 1.1rem;
      }
    }

    .hero-actions {
      .ant-btn {
        height: 48px;
        padding: 0 32px;
        font-size: 16px;
        font-weight: 500;
        border-radius: 24px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 15%);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 25%);
        }
      }

      .ant-btn-primary {
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        border: none;
      }
    }
  }
}

/* 统计数据区域 */
.statistics-section {
  padding: 80px 0;
  background: #f8f9fa;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  .stat-card {
    text-align: center;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 8%);
    transition: all 0.3s ease;
    border: none;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 12%);
    }

    .ant-statistic-title {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .ant-statistic-content {
      .ant-statistic-content-prefix {
        font-size: 20px;
        margin-right: 8px;
      }
    }
  }
}

/* 特色功能区域 */
.features-section {
  padding: 80px 0;
  background: white;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 60px;

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #333;
      margin-bottom: 16px;
    }

    .section-description {
      font-size: 1.1rem;
      color: #666;
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .feature-card {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 8%);
    transition: all 0.3s ease;
    border: none;
    height: 100%;

    .feature-image {
      position: relative;
      height: 200px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      .feature-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 10%);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        .feature-icon {
          font-size: 48px;
          color: white;
          transition: all 0.3s ease;
        }
      }

      &:hover {
        transform: scale(1.02);

        .feature-overlay {
          background: rgba(0, 0, 0, 20%);

          .feature-icon {
            font-size: 56px;
            transform: scale(1.1);
          }
        }
      }
    }

    .ant-card-body {
      padding: 24px;

      .feature-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;

        .feature-arrow {
          opacity: 0;
          transition: all 0.3s ease;
          color: #1890ff;
        }
      }

      .ant-card-meta-description {
        color: #666;
        line-height: 1.6;
        margin-top: 8px;
      }
    }

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 32px rgba(0, 0, 0, 15%);

      .feature-arrow {
        opacity: 1;
        transform: translateX(4px);
      }
    }
  }
}

/* 地图展示区域 */
.map-section {
  padding: 80px 0;
  background: #f8f9fa;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  .section-header {
    text-align: center;
    margin-bottom: 60px;

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #333;
      margin-bottom: 16px;
    }

    .section-description {
      font-size: 1.1rem;
      color: #666;
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .map-card {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 10%);
    border: none;

    .ant-card-body {
      padding: 0;
    }

    .map-container {
      width: 100%;
      height: 600px;
      border-radius: 16px;
      overflow: hidden;

      @media (max-width: 768px) {
        height: 400px;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-section,
  .features-section,
  .map-section {
    padding: 60px 0;
  }

  .features-section {
    .section-header {
      margin-bottom: 40px;

      .section-title {
        font-size: 2rem;
      }
    }
  }

  .map-section {
    .section-header {
      margin-bottom: 40px;

      .section-title {
        font-size: 2rem;
      }
    }
  }
}

/* 动画效果 */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-content,
.stat-card,
.feature-card,
.map-card {
  animation: fade-in-up 0.6s ease-out;
}

.stat-card {
  animation-delay: 0.1s;
}

.feature-card {
  &:nth-child(1) {
    animation-delay: 0.2s;
  }

  &:nth-child(2) {
    animation-delay: 0.3s;
  }

  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

.map-card {
  animation-delay: 0.5s;
}

/* 页脚区域 */
.footer-section {
  padding: 60px 0 40px;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  .footer-brand {
    .ant-typography {
      color: white;
    }
  }

  .footer-links {
    .footer-nav {
      display: flex;
      flex-direction: column;
      gap: 12px;

      a {
        color: rgba(255, 255, 255, 80%);
        text-decoration: none;
        transition: color 0.3s ease;
        cursor: pointer;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }

  .footer-contact {
    .footer-info {
      p {
        color: rgba(255, 255, 255, 80%);
        margin-bottom: 8px;
        line-height: 1.6;
      }
    }
  }

  .footer-bottom {
    text-align: center;
    margin-top: 20px;
  }
}

/* 回到顶部按钮 */
.back-top-button {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 30%);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(24, 144, 255, 40%);
  }
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 全局动画优化 */
* {
  transition: all 0.3s ease;
}
