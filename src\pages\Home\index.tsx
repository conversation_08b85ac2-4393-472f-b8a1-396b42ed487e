import GaoDeMap from '@/components/GaoDeMap';
import PublicLayout from '@/components/PublicLayout';
import { mapData } from '@/services/mockData';
import {
  ArrowRightOutlined,
  DatabaseOutlined,
  EnvironmentOutlined,
  EyeOutlined,
  GlobalOutlined,
  HistoryOutlined,
  UpOutlined,
} from '@ant-design/icons';
import { history } from '@umijs/max';
import {
  BackTop,
  Button,
  Card,
  Col,
  Divider,
  Row,
  Space,
  Statistic,
  Typography,
} from 'antd';
import React from 'react';
import './index.less';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  // 生成信息窗口内容，包含图片和查看详情按钮
  const generateInfoWindowContent = (item: any, type: string) => {
    const detailUrl = `/detail/${type}/${item.id}`;
    let basicInfo = '';

    if (type === 'mountain') {
      basicInfo = `<p style="margin: 4px 0; color: #666; font-size: 13px;">海拔：${item.height}米</p>`;
    } else if (type === 'waterSystem') {
      basicInfo = `<p style="margin: 4px 0; color: #666; font-size: 13px;">长度：${item.length_area}</p>`;
    } else if (type === 'historicalElement') {
      basicInfo = `<p style="margin: 4px 0; color: #666; font-size: 13px;">建造时间：${new Date(
        item.construction_time,
      ).getFullYear()}年</p>`;
    }

    // 获取第一张图片作为展示图片
    const firstPhoto =
      item.photos && item.photos.length > 0 ? item.photos[0] : null;
    const imageHtml = firstPhoto
      ? `<div style="position: relative; width: 100%; height: 140px; overflow: hidden;">
          <img src="${firstPhoto.url}" alt="${firstPhoto.name || item.name}"
               style="width: 100%; height: 100%; object-fit: cover; display: block;"
               onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
          <div style="display: none; width: 100%; height: 100%; background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%); align-items: center; justify-content: center; color: #999; font-size: 14px;">暂无图片</div>
         </div>`
      : ''; // `<div style="width: 100%; height: 140px; background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%); display: flex; align-items: center; justify-content: center; color: #999; font-size: 14px;">暂无图片</div>`;

    return `<div style="padding: 0; min-width: 240px; max-width: 300px; background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); overflow: hidden;">
      ${imageHtml}
      <div style="padding: 12px;">
        <h4 style="margin: 0 0 6px 0; color: #333; font-size: 16px; font-weight: bold; line-height: 1.3;">${item.name}</h4>
        ${basicInfo}
        <p style="margin: 6px 0 12px 0; color: #666; font-size: 13px; line-height: 1.4; max-height: 54px; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical;">${item.historical_records}</p>
        <div style="text-align: center;">
          <a href="${detailUrl}" style="display: inline-block; padding: 8px 20px; background: #1890ff; color: white; text-decoration: none; border-radius: 4px; font-size: 13px; font-weight: 500; transition: all 0.3s ease;"
             onmouseover="this.style.backgroundColor='#40a9ff'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(24,144,255,0.3)';"
             onmouseout="this.style.backgroundColor='#1890ff'; this.style.transform='translateY(0)'; this.style.boxShadow='none';">查看详情</a>
        </div>
      </div>
    </div>`;
  };

  // 准备地图标记点数据
  const prepareMapMarkers = () => {
    const markers: any[] = [];

    // 添加山塬标记点
    mapData.mountains.forEach((mountain) => {
      markers.push({
        position: [mountain.longitude, mountain.latitude],
        title: mountain.name,
        content: generateInfoWindowContent(mountain, 'mountain'),
        id: `mountain_${mountain.id}`,
        status: 1, // 山塬状态
        icon: {
          image: AMAP_CONFIG.markerIcons.mountain,
          size: [32, 32],
          offset: [16, 32],
        },
        data: { ...mountain, type: 'mountain' },
      });
    });

    // 添加水系标记点
    mapData.waterSystems.forEach((waterSystem) => {
      markers.push({
        position: [waterSystem.longitude, waterSystem.latitude],
        title: waterSystem.name,
        content: generateInfoWindowContent(waterSystem, 'waterSystem'),
        id: `water_${waterSystem.id}`,
        status: 2, // 水系状态
        icon: {
          image: AMAP_CONFIG.markerIcons.water,
          size: [32, 32],
          offset: [16, 32],
        },
        data: { ...waterSystem, type: 'waterSystem' },
      });
    });

    // 添加历史要素标记点
    mapData.historicalElements.forEach((element) => {
      markers.push({
        position: [
          element.construction_longitude,
          element.construction_latitude,
        ],
        title: element.name,
        content: generateInfoWindowContent(element, 'historicalElement'),
        id: `historical_${element.id}`,
        status: 3, // 历史要素状态
        icon: {
          image: AMAP_CONFIG.markerIcons.historical,
          size: [32, 32],
          offset: [16, 32],
        },
        data: { ...element, type: 'historicalElement' },
      });
    });

    return markers;
  };

  // 地图标记点点击事件处理
  const handleMarkerClick = (marker: any, e: any) => {
    console.log('标记点点击:', marker, e);
    // 不再显示自定义Modal，统一使用地图信息窗口
  };

  // 地图点击事件处理
  const handleMapClick = (e: any) => {
    console.log('地图点击:', e);
  };

  // 地图创建完成回调
  const handleMapCreated = (mapInstance: any) => {
    console.log('地图创建完成:', mapInstance);
  };

  // 导航到详情页面
  const navigateToSection = (path: string) => {
    history.push(path);
  };

  // 统计数据
  const statisticsData = [
    {
      title: '山塬数量',
      value: mapData.mountains.length,
      icon: <EnvironmentOutlined style={{ color: '#52c41a' }} />,
      suffix: '座',
    },
    {
      title: '水系数量',
      value: mapData.waterSystems.length,
      icon: <GlobalOutlined style={{ color: '#1890ff' }} />,
      suffix: '条',
    },
    {
      title: '历史要素',
      value: mapData.historicalElements.length,
      icon: <HistoryOutlined style={{ color: '#fa8c16' }} />,
      suffix: '处',
    },
    {
      title: '数据总量',
      value:
        mapData.mountains.length +
        mapData.waterSystems.length +
        mapData.historicalElements.length,
      icon: <DatabaseOutlined style={{ color: '#722ed1' }} />,
      suffix: '项',
    },
  ];

  // 特色功能卡片
  const featureCards = [
    {
      title: '山塬地貌',
      description:
        '探索关中地区独特的山塬地貌，了解地形特征与历史文化的深度融合',
      icon: <EnvironmentOutlined />,
      color: '#52c41a',
      path: '/mountain',
      gradient: 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)',
    },
    {
      title: '水系网络',
      description: '深入了解关中地区的水系分布，感受水文化对区域发展的重要影响',
      icon: <GlobalOutlined />,
      color: '#1890ff',
      path: '/water-system',
      gradient: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
    },
    {
      title: '历史要素',
      description: '追溯千年历史足迹，探寻古建筑与文物背后的文明智慧',
      icon: <HistoryOutlined />,
      color: '#fa8c16',
      path: '/historical-element',
      gradient: 'linear-gradient(135deg, #fa8c16 0%, #d46b08 100%)',
    },
  ];

  return (
    <PublicLayout>
      {/* Hero Banner */}
      <div className="hero-banner">
        <div className="hero-content">
          <div className="hero-text">
            <Title level={1} className="hero-title">
              关中地区智慧营建系统
            </Title>
            <Paragraph className="hero-description">
              探索关中地区的山塬、水系与历史要素，感受千年文明的智慧传承
            </Paragraph>
            <Space size="large" className="hero-actions">
              <Button
                type="primary"
                size="large"
                icon={<EyeOutlined />}
                onClick={() => navigateToSection('/digital')}
              >
                数据可视化
              </Button>
              <Button
                size="large"
                icon={<ArrowRightOutlined />}
                onClick={() => navigateToSection('/mountain')}
              >
                开始探索
              </Button>
            </Space>
          </div>
        </div>
        <div className="hero-overlay"></div>
      </div>

      {/* 统计数据 */}
      <div className="statistics-section">
        <div className="container">
          <Row gutter={[24, 24]}>
            {statisticsData.map((stat, index) => (
              <Col xs={12} sm={6} key={index}>
                <Card className="stat-card">
                  <Statistic
                    title={stat.title}
                    value={stat.value}
                    suffix={stat.suffix}
                    prefix={stat.icon}
                    valueStyle={{ fontSize: '24px', fontWeight: 'bold' }}
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* 特色功能 */}
      <div className="features-section">
        <div className="container">
          <div className="section-header">
            <Title level={2} className="section-title">
              核心功能
            </Title>
            <Paragraph className="section-description">
              深入了解关中地区的地理文化要素
            </Paragraph>
          </div>

          <Row gutter={[24, 24]}>
            {featureCards.map((feature, index) => (
              <Col xs={24} md={8} key={index}>
                <Card
                  className="feature-card"
                  hoverable
                  cover={
                    <div
                      className="feature-image"
                      style={{ background: feature.gradient }}
                    >
                      <div className="feature-overlay">
                        <div
                          className="feature-icon"
                          style={{ color: 'white' }}
                        >
                          {feature.icon}
                        </div>
                      </div>
                    </div>
                  }
                  onClick={() => navigateToSection(feature.path)}
                >
                  <Card.Meta
                    title={
                      <div className="feature-title">
                        {feature.title}
                        <ArrowRightOutlined className="feature-arrow" />
                      </div>
                    }
                    description={feature.description}
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* 地图展示 */}
      <div className="map-section">
        <div className="container">
          <div className="section-header">
            <Title level={2} className="section-title">
              地理分布
            </Title>
            <Paragraph className="section-description">
              点击地图标记点查看详细信息
            </Paragraph>
          </div>

          <Card className="map-card">
            <div className="map-container">
              <GaoDeMap
                city="西安"
                center={[108.9398, 34.3412]}
                zoom={8}
                activedZoom={12}
                markers={prepareMapMarkers()}
                enableInfoWindow={true}
                enableCluster={false}
                events={{
                  onClick: handleMapClick,
                  onMarkerClick: handleMarkerClick,
                }}
                onMapCreated={handleMapCreated}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          </Card>
        </div>
      </div>

      {/* 页脚信息 */}
      <div className="footer-section">
        <div className="container">
          <Row gutter={[48, 32]}>
            <Col xs={24} md={8}>
              <div className="footer-brand">
                <Title level={4} style={{ color: 'white', marginBottom: 16 }}>
                  智慧营建系统
                </Title>
                <Paragraph style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                  探索关中地区的山塬、水系与历史要素，感受千年文明的智慧传承
                </Paragraph>
              </div>
            </Col>
            <Col xs={24} md={8}>
              <div className="footer-links">
                <Title level={5} style={{ color: 'white', marginBottom: 16 }}>
                  快速导航
                </Title>
                <div className="footer-nav">
                  <a onClick={() => navigateToSection('/mountain')}>山塬地貌</a>
                  <a onClick={() => navigateToSection('/water-system')}>
                    水系网络
                  </a>
                  <a onClick={() => navigateToSection('/historical-element')}>
                    历史要素
                  </a>
                  <a onClick={() => navigateToSection('/digital')}>数字化</a>
                </div>
              </div>
            </Col>
            <Col xs={24} md={8}>
              <div className="footer-contact">
                <Title level={5} style={{ color: 'white', marginBottom: 16 }}>
                  系统信息
                </Title>
                <div className="footer-info">
                  <p>基于现代Web技术构建</p>
                  <p>支持多终端访问</p>
                  <p>实时数据更新</p>
                </div>
              </div>
            </Col>
          </Row>
          <Divider
            style={{
              borderColor: 'rgba(255, 255, 255, 0.2)',
              margin: '40px 0 20px',
            }}
          />
          <div className="footer-bottom">
            <Paragraph
              style={{
                color: 'rgba(255, 255, 255, 0.6)',
                textAlign: 'center',
                margin: 0,
              }}
            >
              © 2024 关中地区智慧营建系统. All rights reserved.
            </Paragraph>
          </div>
        </div>
      </div>

      {/* 回到顶部按钮 */}
      <BackTop>
        <div className="back-top-button">
          <UpOutlined />
        </div>
      </BackTop>
    </PublicLayout>
  );
};

export default HomePage;
