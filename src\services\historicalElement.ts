import { request } from '@umijs/max';

// ==================== 类型定义 ====================

export interface HistoricalElement {
  id: number;
  name: string;
  code: string;
  typeDictId?: number;
  constructionLongitude: number;
  constructionLatitude: number;
  locationDescription?: string;
  constructionTime?: string;
  historicalRecords?: string;
  regionDictId: number;
  typeDict?: {
    id: number;
    typeName: string;
    typeCode: string;
  };
  regionDict?: {
    id: number;
    regionName: string;
    regionCode: string;
  };
  photos?: Array<{
    id: number;
    name: string;
    url: string;
  }>;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateHistoricalElementParams {
  name: string;
  code: string;
  typeDictId?: number;
  constructionLongitude: number;
  constructionLatitude: number;
  locationDescription?: string;
  constructionTime?: string;
  historicalRecords?: string;
  regionDictId: number;
}

export interface UpdateHistoricalElementParams {
  name?: string;
  code?: string;
  typeDictId?: number;
  constructionLongitude?: number;
  constructionLatitude?: number;
  locationDescription?: string;
  constructionTime?: string;
  historicalRecords?: string;
  regionDictId?: number;
}

export interface GetHistoricalElementListParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  regionId?: number;
  typeId?: number;
}

export interface HistoricalElementListResponse {
  data: HistoricalElement[];
  total: number;
  page: number;
  pageSize: number;
}

export interface BatchImportParams {
  elements: CreateHistoricalElementParams[];
}

export interface HistoricalElementStatistics {
  total: number;
  byType: Array<{
    typeId: number;
    typeName: string;
    count: number;
  }>;
  byRegion: Array<{
    regionId: number;
    regionName: string;
    count: number;
  }>;
  byPeriod: Array<{
    period: string;
    count: number;
  }>;
}

export interface TimelineData {
  year: number;
  count: number;
  elements: Array<{
    id: number;
    name: string;
    type: string;
  }>;
}

export interface GetTimelineParams {
  regionId?: number;
}

export interface GetByConstructionTimeParams {
  startTime?: string;
  endTime?: string;
}

export interface GetStatisticsParams {
  regionId?: number;
  typeId?: number;
}

// ==================== API 接口 ====================

/**
 * 创建历史要素
 */
export async function createHistoricalElement(
  params: CreateHistoricalElementParams,
): Promise<API.ResType<HistoricalElement>> {
  return request('/admin/historical-element', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新历史要素
 */
export async function updateHistoricalElement(
  id: number,
  params: UpdateHistoricalElementParams,
): Promise<API.ResType<HistoricalElement>> {
  return request(`/admin/historical-element/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除历史要素
 */
export async function deleteHistoricalElement(
  id: number,
  deletePhotos?: boolean,
): Promise<API.ResType<{ message: string; deletedPhotos: boolean }>> {
  const params = new URLSearchParams();
  if (deletePhotos) {
    params.append('deletePhotos', 'true');
  }
  
  const url = `/admin/historical-element/${id}${
    params.toString() ? `?${params.toString()}` : ''
  }`;
  
  return request(url, {
    method: 'DELETE',
  });
}

/**
 * 获取历史要素列表
 */
export async function getHistoricalElementList(
  params?: GetHistoricalElementListParams,
): Promise<API.ResType<HistoricalElementListResponse>> {
  const queryParams = new URLSearchParams();
  if (params?.page) queryParams.append('page', params.page.toString());
  if (params?.pageSize) queryParams.append('pageSize', params.pageSize.toString());
  if (params?.keyword) queryParams.append('keyword', params.keyword);
  if (params?.regionId) queryParams.append('regionId', params.regionId.toString());
  if (params?.typeId) queryParams.append('typeId', params.typeId.toString());

  const url = `/admin/historical-element${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;
  
  return request(url, {
    method: 'GET',
  });
}

/**
 * 获取历史要素详情
 */
export async function getHistoricalElementDetail(
  id: number,
): Promise<API.ResType<HistoricalElement>> {
  return request(`/admin/historical-element/${id}`, {
    method: 'GET',
  });
}

/**
 * 批量导入历史要素
 */
export async function batchImportHistoricalElements(
  params: BatchImportParams,
): Promise<API.ResType<{ message: string }>> {
  return request('/admin/historical-element/batch-import', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取历史要素统计
 */
export async function getHistoricalElementStatistics(
  params?: GetStatisticsParams,
): Promise<API.ResType<HistoricalElementStatistics>> {
  const queryParams = new URLSearchParams();
  if (params?.regionId) queryParams.append('regionId', params.regionId.toString());
  if (params?.typeId) queryParams.append('typeId', params.typeId.toString());

  const url = `/admin/historical-element/statistics/overview${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;
  
  return request(url, {
    method: 'GET',
  });
}

/**
 * 根据类型获取历史要素
 */
export async function getHistoricalElementsByType(
  typeId: number,
): Promise<API.ResType<HistoricalElement[]>> {
  return request(`/admin/historical-element/by-type/${typeId}`, {
    method: 'GET',
  });
}

/**
 * 根据区域获取历史要素
 */
export async function getHistoricalElementsByRegion(
  regionId: number,
): Promise<API.ResType<HistoricalElement[]>> {
  return request(`/admin/historical-element/by-region/${regionId}`, {
    method: 'GET',
  });
}

/**
 * 根据建造时间范围查询历史要素
 */
export async function getHistoricalElementsByConstructionTime(
  params: GetByConstructionTimeParams,
): Promise<API.ResType<HistoricalElement[]>> {
  const queryParams = new URLSearchParams();
  if (params.startTime) queryParams.append('startTime', params.startTime);
  if (params.endTime) queryParams.append('endTime', params.endTime);

  const url = `/admin/historical-element/by-construction-time${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;
  
  return request(url, {
    method: 'GET',
  });
}

/**
 * 获取时间轴数据
 */
export async function getHistoricalElementTimeline(
  params?: GetTimelineParams,
): Promise<API.ResType<TimelineData[]>> {
  const queryParams = new URLSearchParams();
  if (params?.regionId) queryParams.append('regionId', params.regionId.toString());

  const url = `/admin/historical-element/timeline${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;
  
  return request(url, {
    method: 'GET',
  });
}
