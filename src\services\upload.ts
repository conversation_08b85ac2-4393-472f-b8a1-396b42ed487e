import { request } from '@umijs/max';

// 上传相关的API接口

/**
 * 上传文件响应类型
 */
export interface UploadFileResponse {
  url: string;
  filename: string;
  size: number;
  photoId?: number; // 当createPhoto=true时返回
}

/**
 * 照片记录类型
 */
export interface PhotoRecord {
  id: number;
  name: string;
  url: string;
  path?: string;
  mountainId?: number;
  waterSystemId?: number;
  historicalElementId?: number;
  mountain?: { id: number; name: string };
  waterSystem?: { id: number; name: string };
  historicalElement?: { id: number; name: string };
  createdAt: string;
  updatedAt: string;
}

/**
 * 照片列表响应类型
 */
export interface PhotoListResponse {
  list: PhotoRecord[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * 照片统计响应类型
 */
export interface PhotoStatistics {
  total: number;
  mountainPhotos: number;
  waterSystemPhotos: number;
  historicalElementPhotos: number;
  unassignedPhotos: number;
}

/**
 * 上传照片文件
 * 根据新API文档，上传接口已变更为 /admin/photo/upload
 * 默认创建照片记录，移除了createPhoto参数
 */
export async function uploadFile(
  file: File,
  options?: {
    photoName?: string;
    entityType?: 'mountain' | 'waterSystem' | 'historicalElement';
    entityId?: number;
  },
): Promise<API.ResType<UploadFileResponse>> {
  const formData = new FormData();
  formData.append('file', file);

  // 构建查询参数
  const params = new URLSearchParams();
  if (options?.photoName) {
    params.append('photoName', options.photoName);
  }
  if (options?.entityType) {
    params.append('entityType', options.entityType);
  }
  if (options?.entityId) {
    params.append('entityId', options.entityId.toString());
  }

  const url = `/admin/photo/upload${
    params.toString() ? `?${params.toString()}` : ''
  }`;

  return request(url, {
    method: 'POST',
    data: formData,
    // 不要手动设置Content-Type，让浏览器自动设置multipart/form-data边界
  });
}

/**
 * 文件上传工具函数
 */
export const uploadUtils = {
  /**
   * 检查文件类型是否支持
   */
  isImageFile: (file: File): boolean => {
    const supportedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/webp',
    ];
    return supportedTypes.includes(file.type);
  },

  /**
   * 检查文件大小是否符合要求
   */
  isValidSize: (file: File, maxSize: number = 50 * 1024 * 1024): boolean => {
    return file.size <= maxSize;
  },

  /**
   * 格式化文件大小
   */
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * 获取文件扩展名
   */
  getFileExtension: (filename: string): string => {
    return filename.slice(((filename.lastIndexOf('.') - 1) >>> 0) + 2);
  },

  /**
   * 验证文件
   */
  validateFile: (file: File): { valid: boolean; message?: string } => {
    if (!uploadUtils.isImageFile(file)) {
      return {
        valid: false,
        message:
          '不支持的文件类型，仅支持图片文件（.jpg, .jpeg, .png, .gif, .bmp, .webp）',
      };
    }

    if (!uploadUtils.isValidSize(file)) {
      return {
        valid: false,
        message: '文件大小超过限制（50MB）',
      };
    }

    return { valid: true };
  },
};

/**
 * 删除照片文件
 * 根据新API文档，删除照片文件和数据库记录
 */
export async function deletePhotoFile(
  id: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/photo/${id}/file`, {
    method: 'DELETE',
  });
}

// ==================== 照片管理 API ====================

/**
 * 创建照片记录
 */
export async function createPhoto(data: {
  name: string;
  url: string;
  mountainId?: number;
  waterSystemId?: number;
  historicalElementId?: number;
}): Promise<API.ResType<PhotoRecord>> {
  return request('/admin/photo', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

/**
 * 更新照片记录
 */
export async function updatePhoto(
  id: number,
  data: {
    name?: string;
    url?: string;
    mountainId?: number;
    waterSystemId?: number;
    historicalElementId?: number;
  },
): Promise<API.ResType<PhotoRecord>> {
  return request(`/admin/photo/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

/**
 * 删除照片记录
 */
export async function deletePhoto(
  id: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/photo/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取照片列表
 */
export async function getPhotoList(params?: {
  page?: number;
  pageSize?: number;
  keyword?: string;
}): Promise<API.ResType<PhotoListResponse>> {
  const queryParams = new URLSearchParams();
  if (params?.page) queryParams.append('page', params.page.toString());
  if (params?.pageSize)
    queryParams.append('pageSize', params.pageSize.toString());
  if (params?.keyword) queryParams.append('keyword', params.keyword);

  const url = `/admin/photo${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;
  return request(url, {
    method: 'GET',
  });
}

/**
 * 获取照片详情
 */
export async function getPhotoDetail(
  id: number,
): Promise<API.ResType<PhotoRecord>> {
  return request(`/admin/photo/${id}`, {
    method: 'GET',
  });
}

/**
 * 根据实体获取照片
 */
export async function getPhotosByEntity(
  entityType: 'mountain' | 'waterSystem' | 'historicalElement',
  entityId: number,
): Promise<API.ResType<PhotoRecord[]>> {
  return request(`/admin/photo/entity/${entityType}/${entityId}`, {
    method: 'GET',
  });
}

/**
 * 获取照片统计
 */
export async function getPhotoStatistics(): Promise<
  API.ResType<PhotoStatistics>
> {
  return request('/admin/photo/statistics/overview', {
    method: 'GET',
  });
}
