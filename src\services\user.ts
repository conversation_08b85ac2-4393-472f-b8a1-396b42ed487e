import { request } from '@umijs/max';

// 用户管理相关的API接口

/**
 * 用户信息类型定义
 */
export interface UserInfo {
  id: number;
  username: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * 用户列表响应类型
 */
export interface UserListResponse {
  list: UserInfo[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * 创建用户参数
 */
export interface CreateUserParams {
  username: string;
  password: string;
  role?: string;
  isActive?: boolean;
}

/**
 * 更新用户参数
 */
export interface UpdateUserParams {
  username?: string;
  password?: string;
  role?: string;
  isActive?: boolean;
}

/**
 * 获取用户列表参数
 */
export interface GetUserListParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
}

/**
 * 创建用户
 */
export async function createUser(
  params: CreateUserParams,
): Promise<API.ResType<UserInfo>> {
  return request('/admin/user', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取用户列表
 */
export async function getUserList(
  params?: GetUserListParams,
): Promise<API.ResType<UserListResponse>> {
  return request('/admin/user', {
    method: 'GET',
    params,
  });
}

/**
 * 获取用户详情
 */
export async function getUserDetail(
  id: number,
): Promise<API.ResType<UserInfo>> {
  return request(`/admin/user/${id}`, {
    method: 'GET',
  });
}

/**
 * 更新用户
 */
export async function updateUser(
  id: number,
  params: UpdateUserParams,
): Promise<API.ResType<UserInfo>> {
  return request(`/admin/user/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除用户
 */
export async function deleteUser(id: number): Promise<API.ResType<null>> {
  return request(`/admin/user/${id}`, {
    method: 'DELETE',
  });
}
