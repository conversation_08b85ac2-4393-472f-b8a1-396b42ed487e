import { request } from '@umijs/max';

const TOKEN_KEY = 'zhyj_access_token';

// 用于存储刷新token的Promise
let refreshTokenPromise: Promise<string | null> | null = null;

export function getToken(): string | null {
  return localStorage.getItem(TOKEN_KEY);
}

export function setToken(token: string) {
  localStorage.setItem(TOKEN_KEY, token);
}

export function removeToken() {
  localStorage.removeItem(TOKEN_KEY);
  refreshTokenPromise = null;
}

// 刷新token
export async function refreshToken(): Promise<string | null> {
  try {
    // 如果已经在刷新，返回正在进行的Promise
    if (refreshTokenPromise) {
      return refreshTokenPromise;
    }

    const token = getToken();
    if (!token) {
      throw new Error('No token');
    }

    // 创建新的刷新Promise
    refreshTokenPromise = new Promise((resolve, reject) => {
      (async () => {
        try {
          const res = await request<API.ResType<{ message: string }>>(
            '/admin/auth/refresh',
            {
              method: 'POST',
            },
          );

          if (res.errCode === 0) {
            // 刷新成功，token会在响应头中返回，由拦截器处理
            resolve(token);
          } else {
            // 刷新失败，直接抛出错误
            throw new Error(res.msg || '刷新token失败');
          }
        } catch (error) {
          reject(error);
        } finally {
          // 完成后清空Promise
          refreshTokenPromise = null;
        }
      })();
    });

    return refreshTokenPromise;
  } catch (error) {
    // 发生错误时清除token
    removeToken();
    throw error; // 向上抛出错误，让调用方处理跳转
  }
}

export async function logout() {
  try {
    // 调用后台退出登录接口
    await request('/admin/auth/logout', {
      method: 'POST',
    });
  } catch (error) {
    // 即使接口调用失败，也要清除本地token
    console.error('退出登录接口调用失败:', error);
  } finally {
    // 清除所有相关的本地存储
    removeToken();
    // 清除可能的其他缓存
    localStorage.clear();
    sessionStorage.clear();
    // 强制刷新页面以确保状态完全重置
    window.location.href = '/admin/login';
  }
}
